-- 房卡抢购脚本
-- 功能：自动在交易行抢购房卡
-- 流程：
-- 1. 打开交易行
-- 2. 选择房卡类别
-- 3. 识别价格
-- 4. 执行购买
-- 5. 处理异常情况

local 工具函数 = require("工具函数")
local OCR功能 = require("OCR功能") -- 导入OCR功能模块

-- 增加常量定义
local MAX_RETRY_COUNT = 3 -- 最大重试次数
local PRICE_CHECK_DELAY = 500 -- 价格检查延迟
local PURCHASE_DELAY = 800 -- 购买后延迟
local MAX_STALE_REFRESH = 20 -- 最大无目标刷新次数

-- 【修复】确保显示信息函数可用（局部函数）
local function 显示信息(msg, 显示Toast)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    local 格式化消息 = string.format("[%s] %s", 时间戳, msg)
    print(格式化消息)
    
    if 显示Toast ~= false then
        toast(msg)
    end
end

-- 【修复】确保延迟函数可用（局部函数）
local function 延迟(ms)
    sleep(ms or 1000)
end

-- 【修复】确保找图_区域找图函数可用（局部函数）- 符合懒人API文档规范
local function 找图_区域找图(x1, y1, x2, y2, pic, color, threshold, offset, debug_mode)
    -- 【符合文档】findPic参数顺序：x1, y1, x2, y2, pic_name, delta_color, dir, sim
    local index, x, y = findPic(x1, y1, x2, y2, pic, color or "101010", offset or 0, threshold or 0.7)
    if x > -1 and y > -1 then
        return true, x, y
    end
    return false, -1, -1
end

-- 确认进入交易行并导航至收藏夹
local function 确认进入交易行并导航至收藏夹()
	local attempts = 0
	local max_attempts = 10
	
	while attempts < max_attempts do
		attempts = attempts + 1
		
		-- 检查是否已在收藏夹
		local item_visible , _ , _ = findPic(1191 , 451 , 1267 , 698 ,
		"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
		"101010" , 0 , 0.7)
		if item_visible > - 1 then
			显示信息("✓ 已在收藏夹商品列表")
			return true
		end
		
		-- 检查是否在交易行主页
		local fav_button_found , fav_x , fav_y = 找图_区域找图(362 , 118 , 540 , 172 ,
		"扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0.7 , 0)
		if fav_button_found then
			tap(fav_x + 90 , fav_y + 95)
			延迟(2000)
			显示信息("点击我的收藏")
			-- 再次检查是否成功进入收藏夹
			local check_result , _ , _ = findPic(1191 , 451 , 1267 , 698 ,
			"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
			"101010" , 0 , 0.7)
			if check_result > - 1 then
				显示信息("✓ 成功进入收藏夹")
				return true
			end
		end
		
		-- 检查是否可以通过出售按钮确认在交易行
		-- 【符合文档】findMultiColor参数顺序：x1, y1, x2, y2, color, offset_color, dir, sim
		local sell_button_visible = findMultiColor(1174 , 35 , 1273 , 142 , "eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7" , 0 , 0.7)
		if sell_button_visible > - 1 then
			显示信息("✓ (导航检查) 通过'出售'按钮备用方案确认在交易行。")
			-- 既然确认在交易行了，再次尝试点击"我的收藏"
			local fav_button_found , fav_x , fav_y = 找图_区域找图(362 , 118 , 540 , 172 ,
			"扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0.7 , 0)
			if fav_button_found then
				tap(fav_x + 90 , fav_y + 95)
				延迟(2000)
			end
		end
		
		显示信息(string.format("(导航检查) 第 %d 次未确认状态，等待1秒..." , attempts) , false)
		sleep(1000)
	end
	
	显示信息("✗ 导航至收藏夹失败。")
	return false
end

-- 【修复】处理单个商品购买（局部函数，专用于房卡）
local function 房卡_处理单个商品购买()
	local has_refreshed = false
	local 本次购买成功次数 = 0
	local 价格检查次数 = 0
	local ocr_fail_count = 0
	local 连续购买次数 = 0
	
	while 价格检查次数 < 10 do
		价格检查次数 = 价格检查次数 + 1
		
		-- 【关键修复】使用房卡专用的价格识别坐标
		显示信息("使用房卡模式价格识别区域...", false)
		local price_str = OCR功能.ocr_start(959 , 564 , 1188 , 597)
		local price = tonumber(price_str)
		local target_price = _G.房卡输入价格 or 0
		显示信息(string.format("第%d次检查价格: %s, 目标: < %d" , 价格检查次数 , tostring(price) or "N/A" , target_price))
		
		if price == nil then
			显示信息("价格识别失败...")
			ocr_fail_count = ocr_fail_count + 1
			if ocr_fail_count >= 5 then
				显示信息("连续5次价格识别失败，执行界面清理...")
				房卡_清理弹窗()
				ocr_fail_count = 0
			end
			延迟(PRICE_CHECK_DELAY)
			goto continue
		end
		
		ocr_fail_count = 0
		
		if price < target_price then
			显示信息(string.format("价格合适(%d < %d)，执行购买" , price , target_price))
			-- 【符合文档】findMultiColor参数顺序：x1, y1, x2, y2, color, offset_color, dir, sim
			local buy_x, buy_y = findMultiColor(896,524,1277,719,"10cb7e","1|-6|10b673|-5|-6|10cb7e|-13|-7|10cb7e",0,0.9)
			if buy_y > - 1 then
				-- 直接点击购买，不进行数量调整
				tap(buy_x , buy_y)
				延迟(270)

				-- 资金不足检测
				local no_fund_x, no_fund_y = findMultiColor(896,524,1277,719,"cb302f","0|-4|dd302f|7|-5|c33030|12|4|d7302f",0,0.9)
				if no_fund_x > - 1 then
					显示信息("检测到资金不足提示，停止购买。")
					return "FUNDS_INSUFFICIENT"
				end
				
				本次购买成功次数 = 本次购买成功次数 + 1
				连续购买次数 = 连续购买次数 + 1
				显示信息(string.format("购买成功，连续购买第%d次" , 连续购买次数))
				
				延迟(350)
				local new_price_str = OCR功能.ocr_start(959 , 564 , 1188 , 597)
				local new_price = tonumber(new_price_str)
				
				if new_price then
					显示信息(string.format("购买后价格检查: %s" , tostring(new_price)))
					
					if new_price < target_price then
						显示信息(string.format("价格仍然合适(%d < %d)，准备继续购买" , new_price , target_price))
						has_refreshed = false
						延迟(300)
					else
						显示信息(string.format("购买后价格不合适(%d >= %d)，尝试刷新" , new_price , target_price))
						if not has_refreshed then
							local 找到刷新 , refresh_x , refresh_y = 找图_区域找图(28 , 70 , 86 , 126 , "扫货_点击刷新按钮.png" , "202020" , 0.7 , 0 , true)
							if 找到刷新 then
								tap(refresh_x , refresh_y)
								has_refreshed = true
								延迟(300)
								显示信息("已刷新，重新检查价格")
							else
								显示信息("未找到刷新按钮，结束购买")
								break
							end
						else
							显示信息("已刷新过且价格不合适，结束购买")
							break
						end
					end
				else
					显示信息("购买后价格识别失败，等待后重试")
					延迟(300)
				end
			else
				显示信息("购买按钮不可用，商品可能已售罄")
				break
			end
		else
			if not has_refreshed then
				显示信息(string.format("价格不符合(%d >= %d)，尝试刷新一次..." , price , target_price))
				local 找到刷新 , refresh_x , refresh_y = 找图_区域找图(28 , 70 , 86 , 126 , "扫货_点击刷新按钮.png" , "202020" , 0.7 , 0 , true)
				if 找到刷新 then
					tap(refresh_x , refresh_y)
					has_refreshed = true
					延迟(300)
					显示信息("已刷新，重新检查价格")
				else
					显示信息("未找到刷新按钮，放弃此物品")
					break
				end
			else
				显示信息("价格不合适且已刷新过，放弃此物品")
				break
			end
		end
		
		if 连续购买次数 >= 20 then
			显示信息("连续购买次数过多，可能出现异常，结束购买")
			break
		end
		
		::continue::
	end
	
	if 本次购买成功次数 > 0 then
		显示信息(string.format("本次商品购买完成，共成功购买 %d 次" , 本次购买成功次数))
	end
	
	return 本次购买成功次数
end

-- 【修复】返回商品列表（局部函数）
local function 房卡_返回商品列表()
	显示信息("返回商品列表...")
	local back_idx , back_x , back_y = findPic(0 , 0 , 209 , 64 , "扫货_返回代售列表.png|扫货_返回代售列表1.png" , "101010" , 0 , 0.7)
	if back_x > - 1 and back_y > - 1 then
		tap(back_x , back_y)
		延迟(300)
		显示信息("点击返回代售列表")
		local fav_idx , fav_x , fav_y = findPic(362 , 118 , 540 , 172 , "扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0 , 0.7)
		if fav_x > - 1 and fav_y > - 1 then
			tap(fav_x + 85 , fav_y + 90)
			延迟(300)
			显示信息("点击我的收藏")
		end
	end
end

-- 【修复】刷新商品列表（局部函数）
local function 房卡_刷新商品列表()
	local index , x , y = findPic(362 , 118 , 540 , 172 , "扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x + 85 , y + 90)
		显示信息("刷新商品列表")
		延迟(300)
	end
end

-- 【修复】清理各种弹窗（局部函数）
local function 房卡_清理弹窗()
	-- 返回代售列表
	local index , x , y = findPic(0 , 0 , 209 , 64 , "扫货_返回代售列表.png|扫货_返回代售列表1.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(500)
		显示信息("清理：返回到代售列表")
	end
	
	-- 点击我的收藏
	local index , x , y = findPic(362 , 118 , 540 , 172 , "扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x + 85 , y + 90)
		延迟(500)
		显示信息("点击我的收藏")
	end
	
	-- 尝试返回大厅
	local x , y = findMultiColor(270 , 631 , 392 , 703 , "eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(1000)
	end
	
	-- 点击角色信息返回大厅
	local index , x , y = findPic(0 , 3 , 218 , 67 , "扫货_点击角色信息返回大厅.png|扫货_点击角色信息返回大厅1.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(1000)
		显示信息("点击角色信息返回大厅")
	end
	
	-- 关闭兑换弹窗
	local index , x , y = findPic(1174 , 35 , 1273 , 142 , "扫货_关闭兑换.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(1000)
		显示信息("关闭兑换弹窗")
	end
	
	-- 关闭钥匙弹窗
	local index , x , y = findPic(103 , 362 , 377 , 534 , "扫货_关闭钥匙.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(1000)
		显示信息("关闭钥匙弹窗")
	end
	
	-- 关闭配件弹窗
	local 找到 , x , y = 找图_区域找图(118 , 128 , 368 , 215 , "扫货_关闭配件.png" , "101010" , 0.7 , 0 , true)
	if 找到 then
		tap(x , y)
		延迟(1000)
		显示信息("关闭配件弹窗")
	end
	
	-- 点击收藏
	local index , x , y = findPic(118 , 122 , 378 , 211 , "扫货_点击收藏.png" , "101010" , 0 , 0.7)
	if x > - 1 and y > - 1 then
		tap(x , y)
		延迟(1000)
		显示信息("点击收藏")
	end
	
	-- 关闭大厅聊天
	local 找到 , x , y = 找图_区域找图(1203 , 0 , 1279 , 182 , "扫货_聊天返回.png" , "101010" , 0.7 , 0)
	if 找到 then
		tap(1240 , 247)
		显示信息("关闭大厅聊天")
		延迟(500)
	end
end

-- 核心抢购逻辑
local function do_sweep()
	local 购买成功次数 = 0
	local 连续失败次数 = 0
	local 连续未购买次数 = 0
	
	::REINITIALIZE::
	
	-- 全自动挂机模式的特殊处理
	if _G.全自动挂机模式 or _G.使用简化导航 then
		显示信息("检测到全自动挂机模式，使用简化导航流程...")
		
		-- 简化的界面检测，最多尝试5次
		local 简单重试次数 = 0
		local 最大简单重试 = 5
		local 成功进入收藏夹 = false
		
		while 简单重试次数 < 最大简单重试 do
			简单重试次数 = 简单重试次数 + 1
			显示信息(string.format("全自动模式：第%d/%d次尝试进入收藏夹" , 简单重试次数 , 最大简单重试))
			
			-- 首先检查是否已在收藏夹
			local item_visible , _ , _ = findPic(1191 , 451 , 1267 , 698 ,
			"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
			"101010" , 0 , 0.7)
			if item_visible > - 1 then
				显示信息("✓ 全自动模式：已在收藏夹商品列表")
				成功进入收藏夹 = true
				break
			end
			
			-- 检查是否在交易行主页
			local market_open , market_x , market_y = 找图_区域找图(362 , 118 , 540 , 172 ,
			"扫货_点击我的收藏.png|扫货_点击我的收藏1.png" , "101010" , 0.7 , 0)
			if market_open then
				tap(market_x + 90 , market_y + 95)
				延迟(2000)
				显示信息("点击我的收藏")
				
				-- 再次检查是否成功进入收藏夹
				local check_result , _ , _ = findPic(1191 , 451 , 1267 , 698 ,
				"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
				"101010" , 0 , 0.7)
				if check_result > - 1 then
					显示信息("✓ 全自动模式：成功进入收藏夹")
					成功进入收藏夹 = true
					break
				end
			end
			
			-- 尝试返回大厅重新进入
			if 工具函数 and 工具函数.尝试返回大厅 then
			工具函数.尝试返回大厅("房卡抢购初始化")
			else
				-- 备用方案：直接点击返回
				tap(39, 39)
			end
			延迟(1500)
			
			-- 从大厅进入交易行
			local x , y = findMultiColor(270 , 631 , 392 , 703 , "eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7" , 0 , 0.7)
			if x > - 1 and y > - 1 then
				tap(x , y)
				显示信息("点击交易行按钮")
				延迟(3000)
			else
				显示信息("✗ 在大厅未找到交易行按钮")
			end
			
			延迟(1000)
		end
		
		if not 成功进入收藏夹 then
			local final_check , _ , _ = findPic(1191 , 451 , 1267 , 698 ,
			"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
			"101010" , 0 , 0.7)
			if final_check == - 1 then
				显示信息("❌ 全自动模式：无法进入收藏夹，可能界面异常")
				return false
			end
		end
		
	else
		-- 单独抢购模式使用原有的完整导航逻辑
		显示信息("单独抢购模式：使用完整导航流程...")
		
		-- 使用新的可靠导航函数
		显示信息("正在执行界面清理与导航，直至进入交易行收藏夹...")
		local navigation_success = false
		local navigation_attempts = 0
		while navigation_attempts < 3 do
			navigation_attempts = navigation_attempts + 1
			if 确认进入交易行并导航至收藏夹() then
				显示信息("✓ 初始化成功，已进入收藏夹商品列表。")
				navigation_success = true
				break
			end
			
			显示信息("导航失败，将返回大厅重置（第"..navigation_attempts.."/3次尝试）")
			if 工具函数 and 工具函数.尝试返回大厅 then
			工具函数.尝试返回大厅("房卡抢购初始化")
			else
				tap(39, 39)
			end
			延迟(1500)
			
			显示信息("尝试从大厅点击交易行按钮...")
			local x , y = findMultiColor(270 , 631 , 392 , 703 , "eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7" , 0 , 0.7)
			if x > - 1 and y > - 1 then
				tap(x , y)
				显示信息("已点击交易行按钮，等待加载...")
				延迟(3000)
			else
				显示信息("✗ 在大厅未找到交易行按钮。")
			end
		end
		
		if not navigation_success then
			显示信息("❌ 多次尝试后依然无法导航至收藏夹，抢购任务终止。")
			return false
		end
	end
	
	local consecutive_item_not_found = 0
	
	while true do
		if _G.资金不足 then
			显示信息("检测到资金不足标志，终止抢购任务。")
			_G.资金不足 = false
			return false
		end
		
		if 连续失败次数 >= 10 then
			显示信息("❌ 连续失败次数过多，可能出现异常，退出抢购任务。")
			return false
		end
		
		if consecutive_item_not_found >= 5 then
			显示信息("连续5次未找到商品，可能界面异常，将重新初始化...")
			goto REINITIALIZE
		end
		
		local purchased_in_cycle = false
		
		-- 查找房卡项
		local index , x , y = findPic(1191 , 451 , 1267 , 698 ,
		"扫货_选择子弹数量.png|扫货_选择子弹数量1.png|扫货_乌鸡蛋.png|扫货_乌鸡蛋1.png" ,
		"101010" , 0 , 0.7)
		
		if x > - 1 then
			consecutive_item_not_found = 0
			连续失败次数 = 0
			
			--tap(x - 31 , y + 10)
			延迟(350)
			
			local 购买结果 = 房卡_处理单个商品购买() -- 【修复】调用房卡专用函数
			if 购买结果 == "FUNDS_INSUFFICIENT" then
				显示信息("!! 检测到资金不足，正在终止抢购任务...")
				_G.资金不足 = true
				return false
			elseif 购买结果 > 0 then
				purchased_in_cycle = true
				购买成功次数 = 购买成功次数 + 购买结果
				显示信息(string.format("累计成功购买 %d 次" , 购买成功次数))
			end
			
			房卡_返回商品列表() -- 【修复】调用房卡专用函数
		else
			consecutive_item_not_found = consecutive_item_not_found + 1
			显示信息(string.format("未找到可购买的房卡(连续%d次)，刷新列表..." , consecutive_item_not_found))
			房卡_刷新商品列表() -- 【修复】调用房卡专用函数
		end
		
		if purchased_in_cycle then
			连续未购买次数 = 0
		else
			连续未购买次数 = 连续未购买次数 + 1
		end
		
		延迟(200)
	end
end

-- 房卡抢购主函数
local function 房卡抢购(enable_time_limit)
	if 工具函数 and 工具函数.尝试返回大厅 then
	工具函数.尝试返回大厅("准备执行房卡抢购任务")
	else
		tap(39, 39) -- 备用返回方案
	end
	延迟(1500)
	房卡_清理弹窗() -- 【修复】使用房卡专用清理函数
	延迟(500)
	
	_G.全自动挂机模式 = enable_time_limit
	
	local success , result = pcall(do_sweep)
	
	if not success then
		显示信息("!!!!!! 房卡抢购任务发生未知错误 !!!!!!")
		显示信息("错误信息: " .. tostring(result))
		显示信息("正在尝试返回大厅以恢复状态...")
		if 工具函数 and 工具函数.尝试返回大厅 then
		工具函数.尝试返回大厅("房卡抢购任务异常")
		else
			tap(39, 39)
		end
		return false
	end
	
	显示信息("房卡抢购任务流程结束。")
	if 工具函数 and 工具函数.尝试返回大厅 then
	工具函数.尝试返回大厅("房卡抢购任务结束")
	else
		tap(39, 39)
	end
	return result
end

-- 【修复】确保模块正确导出（只导出需要的函数，避免全局污染）
return {
	main = 房卡抢购 ,
	清理弹窗 = 房卡_清理弹窗
}