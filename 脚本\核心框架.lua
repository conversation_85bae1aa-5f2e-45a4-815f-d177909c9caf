-- 三角洲行动 核心框架 v2.1 (健壮性增强版)
-- 提供统一的日志、状态、错误处理和配置管理

local M = {}

-- 版本信息
M.version = "2.1.0"
M.modules = {}
M.globals = {} -- 记录已注册的全局变量
M.config = {}  -- 新增：统一配置中心

-- ==================== 1. 统一日志系统 ====================
local logger_status, Logger = pcall(require, "日志系统")
if not logger_status then
    print("⚠️ 日志系统模块加载失败，使用内置简化版: " .. tostring(Logger))
    -- 创建一个与日志系统接口兼容的简化版
    Logger = {
        init = function() print("✅ 内置简化版日志系统已启动") end,
        info = function(module, msg) print(string.format("[INFO][%s] %s", module, msg)) end,
        warn = function(module, msg) print(string.format("[WARN][%s] %s", module, msg)) end,
        error = function(module, msg, data)
            local err_msg = string.format("[ERROR][%s] %s", module, msg)
            if data and data.stack_trace then
                err_msg = err_msg .. "\n" .. data.stack_trace
            end
            print(err_msg)
        end,
        fatal = function(module, msg) print(string.format("[FATAL][%s] %s", module, msg)) end,
        debug = function(module, msg) print(string.format("[DEBUG][%s] %s", module, msg)) end,
        stateChange = function(module, from, to, reason) print(string.format("[STATE][%s] %s -> %s (%s)", module, from, to, reason)) end,
        performance = function() end, -- 简化版不记录性能
        systemSnapshot = function() end,
        getStats = function() return {} end,
        flush = function() end,
        sessionEnd = function() end
    }
end
M.Logger = Logger

-- ==================== 2. 增强的错误处理 (集成日志) ====================
local error_handler_status, ErrorHandler = pcall(require, "错误处理")
if not error_handler_status then
    -- 如果外部错误处理模块加载失败，使用内置的
    ErrorHandler = { ErrorType = { UI = "UI", LOGIC = "LOGIC", RESOURCE = "RESOURCE", UNKNOWN = "UNKNOWN" } }
end

-- 包装pcall，集成自动日志记录
function M.pcall(func, ...)
    if type(func) ~= "function" then
        M.Logger.error("Framework", "尝试调用一个非函数的值", { value_type = type(func) })
        return false, "target is not a function"
    end
    
    local status, result = pcall(func, ...)
    if not status then
        -- 自动记录错误堆栈
        M.Logger.error("pcall", "执行失败: " .. tostring(result), {
            stack_trace = debug.traceback()
        })
    end
    return status, result
end

-- 安全调用函数，提供模块名
function M.safeCall(func, errorType, moduleName, ...)
    local status, result = M.pcall(func, ...)
    if not status then
        M.Logger.error(moduleName or "UnknownModule", string.format("安全调用失败 (%s): %s", errorType or "UNKNOWN", tostring(result)))
    end
    return status, result
end

-- 带重试的安全调用
function M.retryCall(func, retryCount, retryDelay, errorType, moduleName, ...)
    local attempts = 0
    local maxAttempts = retryCount or 3
    local delay = retryDelay or 1000
    
    while attempts < maxAttempts do
        local status, result = M.pcall(func, ...)
        if status then
            return true, result
        end
        
        attempts = attempts + 1
        M.Logger.warn(moduleName, string.format("调用失败, %dms后重试 (%d/%d)...", delay, attempts, maxAttempts))
        
        if attempts < maxAttempts then
            sleep(delay)
        end
    end
    
    M.Logger.error(moduleName, string.format("达到最大重试次数 (%d) 后仍然失败", maxAttempts))
    return false, "达到最大重试次数"
end

M.ErrorHandler = ErrorHandler

-- ==================== 3. 统一状态管理 (单一数据源) ====================
-- 框架内部的状态表是唯一的数据源
M._state = {}
M.stateManager = nil -- 延迟初始化

local function initializeStateManager()
    local sm_status, StateManager = M.pcall(require, "状态管理")
    if sm_status and StateManager and StateManager.create then
        return StateManager.create(M._state)
    end
    -- 如果状态管理模块加载失败，返回一个简化的管理器
    return {
        get = function(self, key, default_value)
            return M._state[key] or default_value
        end,
        set = function(self, key, value, reason)
            local old_value = M._state[key]
            if old_value ~= value then
                M._state[key] = value
                M.Logger.stateChange("StateManager", tostring(old_value), tostring(value), reason or "未知原因")
            end
        end
    }
end

-- 设置状态
function M.setState(key, value, reason)
    if not M.stateManager then
        M.stateManager = initializeStateManager()
    end
    M.stateManager:set(key, value, reason)
    return value
end

-- 获取状态
function M.getState(key, default)
    if not M.stateManager then
        M.stateManager = initializeStateManager()
    end
    return M.stateManager:get(key, default)
end

-- ==================== 4. 模块与配置管理 ====================
-- 加载模块
M.loadModule = function(name, required)
    if M.modules[name] then
        return M.modules[name]
    end
    
    local status, module = M.pcall(require, name)
    if not status then
        if required then
            M.Logger.fatal("Framework", "必需模块 " .. name .. " 加载失败: " .. tostring(module))
        else
            M.Logger.warn("Framework", "可选模块 " .. name .. " 加载失败: " .. tostring(module))
        end
        return nil
    else
        M.modules[name] = module
        M.Logger.info("Framework", "模块 " .. name .. " 加载成功")
        return module
    end
end

-- 设置配置 (通常在脚本启动时从UI加载)
function M.setConfig(configTable)
    if type(configTable) == "table" then
        M.config = configTable
        M.Logger.info("Framework", "配置已加载", configTable)
        return true
    end
    M.Logger.error("Framework", "设置配置失败: 无效的配置表")
    return false
end

-- 获取单个配置项
function M.getConfig(key, default)
    if M.config and M.config[key] ~= nil then
        return M.config[key]
    end
    return default
end


-- ==================== 辅助工具函数 ====================

-- 安全获取数值
M.safeGetNumber = function(val, default)
    if val == nil then return default or 0 end
    if type(val) == "number" then return val end
    local num = tonumber(val)
    return num ~= nil and num or (default or 0)
end

-- 全局变量注册
M.registerGlobal = function(name, value, source)
    _G[name] = value
    M.globals[name] = {
        source = source or "未知",
        timestamp = os.time()
    }
    return value
end

-- 清理全局变量
M.cleanupGlobals = function()
    local count = 0
    for name, _ in pairs(M.globals) do
        if _G[name] ~= nil then
            _G[name] = nil
            count = count + 1
        end
    end
    M.Logger.info("Framework", "已清理 " .. count .. " 个已注册的全局变量")
    return true
end

-- 文件操作辅助函数
M.fileUtils = {
    fileExists = function(path)
        local f = io.open(path, "r")
        if f then f:close() return true end
        return false
    end,
    readFile = function(path)
        local f, err = io.open(path, "r")
        if not f then return nil, err end
        local content = f:read("*a")
        f:close()
        return content
    end,
    writeFile = function(path, content)
        local f, err = io.open(path, "w")
        if not f then return false, err end
        f:write(content)
        f:close()
        return true
    end,
}

-- 表格工具
M.tableUtils = {
    deepCopy = function(orig)
        local orig_type = type(orig)
        local copy
        if orig_type == 'table' then
            copy = {}
            for orig_key, orig_value in next, orig, nil do
                copy[M.tableUtils.deepCopy(orig_key)] = M.tableUtils.deepCopy(orig_value)
            end
            setmetatable(copy, M.tableUtils.deepCopy(getmetatable(orig)))
        else
            copy = orig
        end
        return copy
    end,
    contains = function(tbl, value)
        for _, v in pairs(tbl) do if v == value then return true end end
        return false
    end,
    length = function(tbl)
        local count = 0
        for _ in pairs(tbl) do count = count + 1 end
        return count
    end
}

-- 系统工具
M.systemUtils = {
    timestamp = function() return os.time() end,
    formatTime = function(ts, fmt) return os.date(fmt or "%T", ts or os.time()) end,
    getMemoryUsage = function() return collectgarbage("count") end
}


-- ==================== 框架初始化 ====================
M.init = function()
    -- 确保日志系统已初始化
    if M.Logger.init then
        M.Logger.init()
    end
    
    -- 初始化状态管理器
    M.stateManager = initializeStateManager()
    M.Logger.info("Framework", "状态管理器初始化完成")
    
    -- 【修复】跳过_G.State的自动迁移，避免递归
    -- 现在_G.State由主脚本管理，不需要框架自动迁移
    -- if _G.State then
    --     M.Logger.warn("Framework", "_G.State 已被弃用，请使用 GameScript.getState/setState。正在迁移旧状态...")
    --     for k, v in pairs(_G.State) do
    --         M.setState(k, v, "从_G.State迁移")
    --     end
    --     -- 为平滑过渡，保留_G.State，但指向框架的内部状态
    --     _G.State = M._state
    -- end

    -- 将自身注册为全局变量以便访问
    M.registerGlobal("GameScript", M, "核心框架")
    
    M.Logger.info("Framework", "核心框架初始化完成，版本: " .. M.version)
    return M
end

return M 