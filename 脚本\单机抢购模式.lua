-- 单机抢购模式模块 (v2.0 Refactored)
local M = {}
M.version = "2.0.0"

-- 获取全局框架引用，如果存在
local GameScript = _G.GameScript
local Logger = _G.Logger
local OCR功能 = _G.OCR功能 -- 提前获取引用

-- =================== 基础辅助函数 ===================

local function 显示信息(msg, 显示Toast)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    local 格式化消息 = string.format("[%s] %s", 时间戳, msg)
    print(格式化消息)
    if 显示Toast then
        toast(msg)
    end
end

local function 延迟(ms)
    sleep(ms or 1000)
end

-- 【修复】符合懒人API文档的区域找图函数
local function 找图_区域找图(x1, y1, x2, y2, pic, color, threshold, offset)
    -- 【符合文档】findPic函数参数顺序：x1, y1, x2, y2, pic_name, delta_color, dir, sim
    -- 根据文档，dir参数：0=从左上向右下查找，1=从中心往四周查找，2=从右下向左上查找等
    local index, x, y = findPic(x1, y1, x2, y2, pic, color or "101010", offset or 0, threshold or 0.7)
    if x > -1 and y > -1 then
        return true, x, y
    end
    return false, -1, -1
end

-- =================== 重构后的核心功能函数 ===================

-- 【修复】优化OCR识别函数，使用框架模块访问
local function 获取价格_OCR(重试次数)
    重试次数 = 重试次数 or 3

    -- 【修复】通过框架访问OCR模块
    local OCR模块 = _G.GameScript and _G.GameScript.modules and _G.GameScript.modules.OCR功能

    for i = 1, 重试次数 do
        if OCR模块 and OCR模块.ocr_start then
            local price_str = OCR模块.ocr_start(978, 81, 1159, 105)
            local price = tonumber(price_str)
            if price and price > 0 then
                return price, price_str
            end
            if i < 重试次数 then
                显示信息(string.format("OCR第%d次识别失败，重试中...", i), false)
                延迟(200)
            end
        else
            显示信息("❌ 错误：OCR功能模块不可用。", false)
            break
        end
    end
    return nil, ""
end

-- 导航函数，用于确认进入交易行并导航至收藏夹
local function 确认进入交易行并导航至收藏夹()
    -- 首次运行时，优先检查是否在大厅，如果是则直接点击交易行
    -- 【符合文档】findMultiColor函数参数顺序：x1, y1, x2, y2, color, offset_color, dir, sim
    local x, y = findMultiColor(270, 631, 392, 703, "eaebeb", "-12|13|e7e8e8|-34|6|d6d7d7", 0, 0.7)
    if x > -1 and y > -1 then
        显示信息("✓ (导航检查) 检测到大厅交易行入口, 尝试点击...", false)
        tap(x, y)
        延迟(3000) -- 等待交易行加载
    end

    local attempts = 0
    while attempts < 8 do -- 总共尝试约8秒
        attempts = attempts + 1

        -- 优先检查是否已在最终目标"收藏夹"
        -- 【符合文档】findPic参数顺序：x1, y1, x2, y2, pic_name, delta_color, dir, sim
        local item_visible, _, _ = findPic(1191, 451, 1267, 698, "扫货_选择子弹数量.png|扫货_选择子弹数量1.png", "101010", 0, 0.7)
        if item_visible > -1 then
             显示信息("✓ (导航检查) 已在收藏夹商品列表。", false)
             return true
        end

        -- 检查"我的收藏"按钮，如果找到就点击
        local market_open, market_x, market_y = 找图_区域找图(362, 118, 540, 172, "扫货_点击我的收藏.png|扫货_点击我的收藏1.png", "101010", 0.7, 0)
        if market_open then
            显示信息("✓ (导航检查) 检测到交易行主页，点击'我的收藏'。", false)
            tap(market_x + 90, market_y + 95)
            延迟(2000) -- 等待收藏夹加载
            -- 再次检查是否成功进入
            local item_check, _, _ = findPic(1191, 451, 1267, 698, "扫货_选择子弹数量.png|扫货_选择子弹数量1.png", "101010", 0, 0.7)
            if item_check > -1 then
                return true
            end
        end

        -- 备用方案：通过"出售"按钮确认在交易行
        local sell_button_visible, _, _ = findPic(7, 130, 117, 227, "出售_点击出售按钮.png|出售_点击出售按钮1.png", "101010", 0, 0.7)
        if sell_button_visible > -1 then
             显示信息("✓ (导航检查) 通过'出售'按钮备用方案确认在交易行。", false)
             -- 既然确认在交易行了，再次尝试点击"我的收藏"
             local fav_button_found, fav_x, fav_y = 找图_区域找图(362, 118, 540, 172, "扫货_点击我的收藏.png|扫货_点击我的收藏1.png", "101010", 0.7, 0)
             if fav_button_found then
                tap(fav_x + 90, fav_y + 95)
                延迟(2000)
             end
        end
        
        显示信息(string.format("(导航检查) 第 %d 次未确认状态，等待1秒...", attempts), false)
        sleep(1000)
    end

    显示信息("✗ 导航至收藏夹失败。", false)
    return false
end

-- 提取出购买操作
local function 执行购买(数量类型, 数量值)
    local 坐标映射 = {
        [31] = {986, 483},
        [200] = {1176, 484}
    }
    local x, y = unpack(坐标映射[数量值] or {986, 483})
    
    显示信息(string.format("点击选择数量%s (%d个)...", 数量类型, 数量值), false)
    tap(x + 1, y)
    延迟(150)
    
    显示信息("点击确认购买...", false)
    tap(1052, 562)
    延迟(300)
    
    -- 点击查看更新后的价格
    显示信息("获取购买后价格...", false)
    tap(1098, 29)
    延迟(200)
    
    return 获取价格_OCR()
end

-- 提取出批量购买逻辑
local function 执行批量购买(起始金额, 目标价格)
    local current_money = 起始金额
    local 连续购买次数 = 0
    while true do
        local new_money, _ = 执行购买("批量", 200)
        if not new_money or new_money >= current_money then
            显示信息("⚠️ 批量购买失败，停止", false)
            break
        end
        local unit_price = (current_money - new_money) / 200
        连续购买次数 = 连续购买次数 + 1
        显示信息(string.format("批量单价: %.2f (第%d次连续购买)", unit_price, 连续购买次数), false)
        if unit_price <= 目标价格 then
            显示信息(string.format("✓ 继续批量购买(%.2f <= %d)", unit_price, 目标价格), false)
            current_money = new_money
            -- 价格符合条件，立即继续下一次购买，不添加延迟
        else
            显示信息(string.format("❌ 价格上涨，停止批量购买(%.2f > %d)", unit_price, 目标价格), false)
            break
        end
    end
    return 连续购买次数
end

-- 提取出资金保护检查逻辑
local low_fund_count = 0 -- 必须在函数外部定义，以保持状态
local function 检查资金保护(current_money, 配置)
    local 资金保护阈值 = tonumber(配置.输入框_资金保护 or "0") or 0
    if 资金保护阈值 > 0 and current_money < 资金保护阈值 then
        low_fund_count = low_fund_count + 1
        local warning_msg = string.format("⚠️ 资金低于阈值，连续检测 %d/5 次", low_fund_count)
        显示信息(warning_msg, false)
        if low_fund_count >= 5 then
            local msg = string.format("💰 资金保护触发！资金(%d) < 阈值(%d)", current_money, 资金保护阈值)
            显示信息(msg, true)
            if Logger then Logger.warn("单机抢购", msg) end
            return false -- 返回 false 表示保护已触发，应终止
        end
        延迟(1000)
        return nil -- 返回 nil 表示需要继续检查，但不执行购买
    else
        low_fund_count = 0
        return true -- 返回 true 表示资金正常
    end
end

-- 【新增】智能重试和恢复机制
local 连续失败计数器 = {
    价格识别失败 = 0,
    测试购买失败 = 0,
    其他错误 = 0,
    总失败次数 = 0
}

local 最大失败阈值 = 5  -- 连续失败5次后触发恢复流程
local 重置计数器阈值 = 10  -- 总失败10次后重置所有计数器

-- 智能恢复流程
local function 执行智能恢复()
    显示信息("🔄 检测到连续失败，启动智能恢复流程...", true)

    -- 1. 尝试返回大厅
    if _G.GameScript and _G.GameScript.modules and _G.GameScript.modules.工具函数 then
        local 工具函数 = _G.GameScript.modules.工具函数
        if 工具函数.尝试返回大厅 then
            显示信息("🏠 正在返回大厅...", true)
            工具函数.尝试返回大厅()
            延迟(3000)  -- 等待返回大厅
        end
    end

    -- 2. 重新导航到交易行收藏夹
    显示信息("🔄 重新导航到交易行收藏夹...", true)
    if not 确认进入交易行并导航至收藏夹() then
        显示信息("❌ 智能恢复失败：无法重新进入收藏夹", true)
        return false
    end

    -- 3. 重置失败计数器
    连续失败计数器.价格识别失败 = 0
    连续失败计数器.测试购买失败 = 0
    连续失败计数器.其他错误 = 0
    显示信息("✅ 智能恢复完成，重置失败计数器", true)

    return true
end

-- 记录失败并检查是否需要恢复
local function 记录失败(失败类型)
    连续失败计数器[失败类型] = (连续失败计数器[失败类型] or 0) + 1
    连续失败计数器.总失败次数 = 连续失败计数器.总失败次数 + 1

    显示信息(string.format("📊 失败统计: %s=%d, 总计=%d",
        失败类型, 连续失败计数器[失败类型], 连续失败计数器.总失败次数), false)

    -- 检查是否需要智能恢复
    if 连续失败计数器[失败类型] >= 最大失败阈值 then
        return 执行智能恢复()
    end

    -- 检查是否需要重置计数器
    if 连续失败计数器.总失败次数 >= 重置计数器阈值 then
        显示信息("🔄 总失败次数过多，重置计数器", true)
        连续失败计数器.价格识别失败 = 0
        连续失败计数器.测试购买失败 = 0
        连续失败计数器.其他错误 = 0
        连续失败计数器.总失败次数 = 0
    end

    return true
end

-- 重构后的核心抢购循环
local function do_purchase_loop(配置)
    local 抢购价格 = tonumber(配置.输入框_单机抢购价格 or "0") or 0
    if 抢购价格 <= 0 then
        显示信息("❌ 错误：单机抢购价格无效（必须大于0）", true)
        return false
    end
    
    -- 获取自定义延迟秒数
    local 抢购延迟 = tonumber(配置.输入框_单机抢购延迟 or "0") or 0
    if 抢购延迟 > 0 then
        显示信息(string.format("⏱️ 已设置抢购延迟：%d秒", 抢购延迟), true)
    end

    if not 确认进入交易行并导航至收藏夹() then
        显示信息("导航至收藏夹失败，任务终止。", true)
        return false
    end
    显示信息("✓✓✓ 已成功进入收藏夹，开始循环抢购...", false)
    显示信息("⏰ 安全保险：2点后将自动停止抢购", true)
    
    while true do
        -- 检查当前时间是否超过凌晨2点
        local 当前时间 = os.date("*t")
        local 当前小时 = 当前时间.hour
        
        if 当前小时 >= 2 and 当前小时 < 6 then
            显示信息("⏰ 已过凌晨2点，安全保险触发，停止抢购", true)
            return true
        end
        
        -- 获取初始价格
        显示信息("获取当前商品价格...", false)
        tap(1098, 29)
        延迟(150)
        
        local initial_money, price_str = 获取价格_OCR()
        if not initial_money then
            显示信息(string.format("⚠️ 价格识别失败: '%s'，刷新列表重试", price_str), false)

            -- 【新增】记录失败并检查是否需要智能恢复
            if not 记录失败("价格识别失败") then
                显示信息("❌ 智能恢复失败，终止抢购", true)
                break
            end

            -- 刷新列表的操作
            local fav_found, fav_x, fav_y = 找图_区域找图(362, 118, 540, 172, "扫货_点击我的收藏.png|扫货_点击我的收藏1.png", "101010", 0.7, 0)
            if fav_found then
                tap(fav_x + 90, fav_y + 95)
            end

            -- 【修复】价格识别失败时也使用自定义延迟
            if 抢购延迟 > 0 then
                显示信息(string.format("⏱️ 价格识别失败，执行自定义延迟：%d秒", 抢购延迟), true)
                sleep(抢购延迟 * 1000)
            else
                延迟(1000)
            end
            goto continue_loop
        end
        
        -- 资金保护检查
        local fund_check_result = 检查资金保护(initial_money, 配置)
        if fund_check_result == false then
            break -- 资金保护触发，终止循环
        elseif fund_check_result == nil then
            goto continue_loop -- 资金低于阈值但未触发保护，跳过本次购买
        end
        
        -- 执行31发测试购买
        local new_money, _ = 执行购买("测试", 31)
        if not new_money or new_money >= initial_money then
            显示信息("⚠️ 测试购买失败或价格异常", false)

            -- 【新增】记录失败并检查是否需要智能恢复
            if not 记录失败("测试购买失败") then
                显示信息("❌ 智能恢复失败，终止抢购", true)
                break
            end

            -- 【修复】测试购买失败时也使用自定义延迟
            if 抢购延迟 > 0 then
                显示信息(string.format("⏱️ 测试购买失败，执行自定义延迟：%d秒", 抢购延迟), true)
                sleep(抢购延迟 * 1000)
            else
                延迟(300)
            end
            goto continue_loop
        end
        
        -- 【新增】成功获取价格，重置价格识别失败计数器
        连续失败计数器.价格识别失败 = 0
        连续失败计数器.测试购买失败 = 0

        -- 计算单价并判断是否合适
        local unit_price = (initial_money - new_money) / 31
        显示信息(string.format("计算单价: %.2f", unit_price), false)
        
        if unit_price <= 抢购价格 then
            显示信息(string.format("✓✓✓ 单价合适(%.2f <= %d)，开始批量购买", unit_price, 抢购价格), true)
            local 购买次数 = 执行批量购买(new_money, 抢购价格)
            显示信息(string.format("✅ 批量购买完成，共执行了%d次购买", 购买次数), true)
            -- 批量购买结束后不添加额外延迟，直接进入下一轮循环
        else
            显示信息(string.format("❌ 单价过高(%.2f > %d)", unit_price, 抢购价格), true)
            
            -- 价格不合适时，使用自定义延迟
            if 抢购延迟 > 0 then
                显示信息(string.format("⏱️ 价格不合适，执行自定义延迟：%d秒", 抢购延迟), true)
                sleep(抢购延迟 * 1000)
            else
                -- 如果没有设置自定义延迟，使用默认延迟
                延迟(300)
            end
        end
        
        ::continue_loop::
    end
    return true
end

-- =================== 模块主入口 ===================
function M.main(配置)
    if Logger then
        Logger.info("单机抢购", "单机抢购模式启动", false)
    end
    print("🎯 单机抢购模式开始执行")
    
    if not 配置 then
        显示信息("❌ 错误：缺少配置信息，无法执行单机抢购", true)
        return
    end

    local success, result = pcall(do_purchase_loop, 配置)
    
    if not success then
        显示信息("!!!!!! 单机抢购任务发生未知错误 !!!!!!", true)
        显示信息("错误信息: " .. tostring(result), true)
    elseif result == false then
        显示信息("任务因配置错误或导航失败等原因提前终止。", true)
    end
    
    显示信息("单机抢购任务流程结束。", false)
    if GameScript and GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
        GameScript.modules.工具函数.尝试返回大厅("单机抢购任务结束")
    end

    return result
end

-- 修复模块导出
return M

