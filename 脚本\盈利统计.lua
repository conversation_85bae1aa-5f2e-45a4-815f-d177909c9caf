-- 【新增】盈利统计功能模块
local 盈利统计模块 = {}

-- 内部状态
local 盈利统计 = {
    初始金额 = 0,
    最终金额 = 0,
    日盈利 = 0,
    最后更新时间 = os.time()
}

-- 统一的显示信息接口
local function 显示信息(msg, 显示Toast)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    local 格式化消息 = string.format("[%s] [盈利统计] %s", 时间戳, msg)
    print(格式化消息)
    
    if 显示Toast ~= false then
        toast(msg)
    end
end

-- 【新增】识别交易行金额
function 盈利统计模块.识别交易行金额()
    local OCR功能 = require("OCR功能") -- 导入OCR功能模块
    local 工具函数 = require("工具函数") -- 导入工具函数
    
    -- 尝试返回大厅确保在正确的界面
    if not 工具函数.尝试返回大厅("识别金额") then
        显示信息("返回大厅失败，无法识别金额", false)
        return nil
    end
    
    -- 点击交易行
    local x, y = findMultiColor(270, 631, 392, 703, "eaebeb", "-12|13|e7e8e8|-34|6|d6d7d7", 0, 0.7)
    if x > -1 and y > -1 then
        tap(x, y)
        sleep(3000) -- 等待交易行加载
    else
        显示信息("未找到交易行按钮", false)
        return nil
    end
    
    -- 点击指定坐标(1098,32)
    tap(1098, 32)
    sleep(1000)
    
    -- 使用OCR识别金额(974,76,1192,106)
    local 识别次数 = 0
    local 最大识别次数 = 3
    local 识别结果 = nil
    
    while 识别次数 < 最大识别次数 do
        识别次数 = 识别次数 + 1
        local 金额文本 = OCR功能.ocr_start(974, 76, 1192, 106)
        显示信息("金额识别结果: " .. (金额文本 or "识别失败"), false)
        
        if 金额文本 and 金额文本 ~= "" then
            -- 去除可能的非数字字符
            金额文本 = string.gsub(金额文本, "[^0-9]", "")
            识别结果 = tonumber(金额文本)
            
            if 识别结果 then
                显示信息("成功识别金额: " .. 识别结果, true)
                break
            end
        end
        
        显示信息("第" .. 识别次数 .. "次金额识别失败，重试", false)
        
        -- 如果识别失败，返回大厅后再次进入
        工具函数.尝试返回大厅("金额识别失败")
        sleep(1500)
        
        -- 重新进入交易行
        local x, y = findMultiColor(270, 631, 392, 703, "eaebeb", "-12|13|e7e8e8|-34|6|d6d7d7", 0, 0.7)
        if x > -1 and y > -1 then
            tap(x, y)
            sleep(3000)
            tap(1098, 32)
            sleep(1000)
        end
    end
    
    -- 返回大厅
    工具函数.尝试返回大厅("金额识别完成")
    
    return 识别结果
end

-- 【新增】记录初始金额
function 盈利统计模块.记录初始金额()
    local 金额 = 盈利统计模块.识别交易行金额()
    if 金额 then
        盈利统计.初始金额 = 金额
        显示信息("初始金额记录成功: " .. 金额, true)
        return true
    else
        显示信息("初始金额记录失败", false)
        return false
    end
end

-- 【新增】记录最终金额并计算盈利
function 盈利统计模块.计算盈利()
    local 金额 = 盈利统计模块.识别交易行金额()
    if 金额 then
        盈利统计.最终金额 = 金额
        
        -- 计算盈利
        if 盈利统计.初始金额 > 0 then
            local 新盈利 = 盈利统计.最终金额 - 盈利统计.初始金额
            盈利统计.日盈利 = 盈利统计.日盈利 + 新盈利
            盈利统计.最后更新时间 = os.time()
            
            显示信息("本次盈利: " .. 新盈利 .. ", 今日盈利: " .. 盈利统计.日盈利, true)
            return true
        else
            显示信息("无初始金额记录，无法计算盈利", false)
            return false
        end
    else
        显示信息("最终金额记录失败，无法计算盈利", false)
        return false
    end
end

-- 【新增】获取盈利信息用于状态显示
function 盈利统计模块.获取盈利信息()
    local 日期 = os.date("%Y-%m-%d", 盈利统计.最后更新时间)
    local 盈利文本 = "💰 今日盈利: " .. 盈利统计.日盈利
    return 盈利文本, 盈利统计.日盈利
end

显示信息("模块已加载")

return 盈利统计模块 