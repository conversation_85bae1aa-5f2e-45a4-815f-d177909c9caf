require("功能插件") -- 导入功能插件模块
local 工具函数 = require("工具函数")
local OCR功能 = require("OCR功能") -- 新增：导入OCR功能模块

-- 定义设备坐标（全局变量）
设备坐标 = {
	["技术中心"] = {x = 280 , y = 545} ,
	["工作台"] = {x = 557 , y = 554} ,
	["制药台"] = {x = 828 , y = 559} ,
	["防具台"] = {x = 1107 , y = 550}
}

-- 定义设备状态区域（全局变量）
设备状态区域 = {
	技术中心 = {160 , 490 , 409 , 624} ,
	工作台 = {426 , 498 , 682 , 626} ,
	制药台 = {712 , 487 , 963 , 627} ,
	防具台 = {977 , 498 , 1242 , 629}
}

-- 【修复】定义设备时间坐标（更新为正确坐标）
设备时间坐标 = {
	技术中心 = {132, 571, 316, 637},
	工作台 = {417, 576, 608, 634},
	制药台 = {683, 569, 860, 640},
	防具台 = {969, 574, 1151, 635}
}

-- 显示信息函数：输出带时间戳的日志，可选择是否显示Toast提示
function 显示信息(msg , 显示Toast)
	local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
	print(string.format("[%s] %s" , 时间戳 , msg))
	
	-- 只在特定情况下显示toast
	if 显示Toast ~= false and (
		string.find(msg , "错误") or
		string.find(msg , "失败") or
		string.find(msg , "✅") or
		string.find(msg , "❌") or
		string.find(msg , "完成") or
		string.find(msg , "启动")
		) then
		toast(msg)
	end
	
	-- 记录到日志文件
	if Logger then
		if string.find(msg , "错误") or string.find(msg , "失败") or string.find(msg , "❌") then
			Logger.error("特勤处制造" , msg)
		elseif string.find(msg , "警告") or string.find(msg , "⚠️") then
			Logger.warn("特勤处制造" , msg)
		else
			Logger.info("特勤处制造" , msg)
		end
	end
end

-- 【新增】仓库整理相关配置
local 整理配置 = {
	区域 = {
		交易行按钮 = {284 , 652 , 376 , 683} ,
		出售按钮 = {7 , 130 , 117 , 227} ,
		整理按钮 = {1039 , 642 , 1279 , 719} ,
		确认整理按钮 = {1039 , 642 , 1279 , 719} ,
		返回大厅 = {0 , 0 , 163 , 53}
	} ,
	颜色 = {
		交易行按钮 = {"eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7"}
	} ,
	图片 = {
		出售按钮 = "出售_点击出售按钮.png|出售_点击出售按钮1.png" ,
		整理按钮 = "出售_点击整理.png|出售_点击整理1.png" ,
		确认整理按钮 = "出售_确认整理.png|出售_确认整理1.png" ,
		返回大厅 = "领取邮件_交易行返回大厅.png|领取邮件_交易行返回大厅1.png"
	}
}

-- 【新增】检查是否已在交易行
local function 检查是否已在交易行()
	local R_sell = 整理配置.区域.出售按钮
	local Img_sell = 整理配置.图片.出售按钮
	local _ , x , y = findPic(R_sell[1] , R_sell[2] , R_sell[3] , R_sell[4] , Img_sell , "101010" , 0 , 0.9)
	if x > - 1 then
		显示信息("✓ 检测到出售按钮，已在交易行" , false)
		return true
	end
	显示信息("✗ 未检测到交易行界面" , false)
	return false
end
-- 【新增】等待界面元素出现
local function 等待界面元素出现(区域 , 图片 , 等待时间 , 描述)
	local 开始时间 = os.time()
	local 超时时间 = 等待时间 or 3
	
	while (os.time() - 开始时间) < 超时时间 do
		local _ , x , y = findPic(区域[1] , 区域[2] , 区域[3] , 区域[4] , 图片 , "101010" , 0 , 0.7)
		if x > - 1 and y > - 1 then
			显示信息(string.format("✓ 成功等待到 %s" , 描述) , false)
			return true , x , y
		end
		sleep(300)
	end
	显示信息(string.format("✗ 等待 %s 超时" , 描述) , false)
	return false , - 1 , - 1
end

-- 【新增】执行仓库整理流程
function 执行仓库整理()
	显示信息("🔄 ═══ 开始仓库整理流程 ═══")
	
	-- 步骤1：检查是否已在交易行，如果不在则打开
	if not 检查是否已在交易行() then
		显示信息("[整理步骤 1/5] 准备打开交易行...")
		
		-- 确保在大厅
		if 工具函数 and 工具函数.尝试返回大厅 then
			工具函数.尝试返回大厅("准备打开交易行进行整理")
			sleep(1500)
		end
		
		-- 查找并点击交易行按钮
		local R_trade = 整理配置.区域.交易行按钮
		local C_trade = 整理配置.颜色.交易行按钮
		local x , y = findMultiColor(R_trade[1] , R_trade[2] , R_trade[3] , R_trade[4] , C_trade[1] , C_trade[2] , 0 , 0.7)
		if x > - 1 then
			显示信息("找到交易行按钮，点击打开...")
			tap(x , y)
			sleep(3000) -- 等待交易行界面加载
			
			-- 验证是否成功进入交易行
			local max_attempts = 3
			for i = 1 , max_attempts do
				if 检查是否已在交易行() then
					显示信息("✓ 成功进入交易行")
					break
				end
				显示信息(string.format("第%d次等待交易行加载..." , i))
				sleep(1000)
				if i == max_attempts then
					显示信息("❌ 无法打开交易行，整理流程失败")
					return false
				end
			end
		else
			显示信息("❌ 未找到交易行按钮，整理流程失败")
			return false
		end
	else
		显示信息("[整理步骤 1/5] 已在交易行，跳过打开步骤")
	end
	
	-- 步骤2：点击出售按钮
	显示信息("[整理步骤 2/5] 正在进入出售界面...")
	local 找到出售 , x , y = 等待界面元素出现(整理配置.区域.出售按钮 , 整理配置.图片.出售按钮 , 3 , "出售按钮")
	if not 找到出售 then 
		显示信息("❌ 未找到出售按钮，整理流程失败")
		return false
	end
	tap(x , y)
	sleep(1500)
	
	-- 步骤3：点击整理按钮
	显示信息("[整理步骤 3/5] 正在整理物品...")
	local 找到整理 , tx , ty = 等待界面元素出现(整理配置.区域.整理按钮 , 整理配置.图片.整理按钮 , 3 , "整理按钮")
	if not 找到整理 then 
		显示信息("❌ 未找到'整理'按钮，整理流程失败")
		return false
	end
	tap(tx , ty)
	sleep(1000)
	
	-- 步骤4：确认整理（可选）
	显示信息("[整理步骤 4/5] 正在确认整理...")
	local 找到确认 , cx , cy = 等待界面元素出现(整理配置.区域.确认整理按钮 , 整理配置.图片.确认整理按钮 , 2 , "确认整理按钮")
	if 找到确认 then
		tap(cx , cy)
		sleep(1500)
		显示信息("✓ 已确认整理")
	else
		显示信息("✓ 未找到'确认整理'按钮，直接继续后续流程")
	end
	
	-- 步骤5：返回大厅
	显示信息("[整理步骤 5/5] 正在返回大厅...")
	if 工具函数 and 工具函数.尝试返回大厅 then
		工具函数.尝试返回大厅("仓库整理完成")
		sleep(2000)
		显示信息("✅ 仓库整理流程完成，已返回大厅")
	else
		-- 备用方案：直接点击返回按钮
		local 找到返回 , rx , ry = 等待界面元素出现(整理配置.区域.返回大厅 , 整理配置.图片.返回大厅 , 3 , "返回大厅按钮")
		if 找到返回 then
			tap(rx , ry)
			sleep(2000)
			显示信息("✅ 仓库整理流程完成，已返回大厅")
		else
			显示信息("⚠️ 整理完成但可能未能返回大厅")
		end
	end
	
	显示信息("✅ ═══ 仓库整理流程完成 ═══")
	return true
end

-- 打开特勤处界面并进入制造页面
function 打开特勤处()
	-- 查找并点击特勤处按钮
	-- local 找到 , x , y = 找色_区域多点找色(511 , 637 , 635 , 707 , "d0d1d1" , "11|3|eaebeb|13|-6|dadbdb|8|-8|cbcccc" , 0 , 0.9 , true)
	-- 显示信息("打开特勤处")
	local x,y = findMultiColor(520,643,641,696,"eaebeb","-2|8|eaebeb|8|12|686b6c",0,0.9)
	if x > -1 and y > -1 then
		tap(x, y)
		显示信息("打开特勤处")
	else
		显示信息("未能找到特勤处按钮，请检查界面")
	end
	延迟()
	
	-- 查找并点击制造按钮
	local 找到 , x , y = 找图_区域找图(0 , 0 , 118 , 236 , "特勤处_点击制造.png" , "101010" , 0.9 , 0 , true)
	if 找到 then
		显示信息("点击制造")
		延迟(2000)
	else
		显示信息("制造已点击")
		延迟(2000)
	end
end
--特勤处_购买
function 特勤处_购买 ()
	-- 执行制造操作（最多尝试10次）
	local 连续未找到次数 = 0
	for i = 1 , 10 , 1 do
		-- 查找制造按钮
		local 找到 , x , y = 找色_区域多点找色(477 , 527 , 841 , 623 , "11c97d" , "-8|3|10cb80|-5|-4|11ad70" , 0 , 0.9 , false)
		if 找到 then
			-- 找到制造按钮后点击
			显示信息(string.format("第%d次找到目标颜色，位置：%d,%d" , i , x , y))
			tap(x , y)
			sleep(1500) -- 点击间隔延迟
			连续未找到次数 = 0 -- 重置未找到计数
		else
			-- 未找到制造按钮的处理
			连续未找到次数 = 连续未找到次数 + 1
			显示信息(string.format("第%d次未找到目标颜色" , i))
			if 连续未找到次数 >= 10 then -- 连续10次未找到则停止尝试
				显示信息("连续3次未找到目标颜色，退出循环")
				延迟(500)
				break
			end
		end
	end
	
end
-- 处理特勤处制造完成后的领取流程
function 特勤处_制造完毕()
	-- 循环检查并领取制造完成的物品（最多8次，增加检测次数）
	local 领取次数 = 0
	local 最大领取次数 = 8
	local 已领取设备 = {} -- 新增：记录已领取的设备
	
	for i = 1 , 最大领取次数 do
		-- 查找制造完成的标志
		local 找到 = false
		local click_x , click_y
		local find_method = ""
		
		-- 仅使用找图
		local 找到图片 , x , y = 找图_区域找图(142 , 479 , 1265 , 653 , "特勤处_制造完成1.png|特勤处_制造完成.png" , "050505" , 0.9 , 0 , false) -- 先不点击
		if 找到图片 then
			找到 = true
			click_x , click_y = x , y
			find_method = "图片"
			
			-- 新增：根据坐标判断是哪个设备
			for 设备名称 , 区域 in pairs(设备状态区域) do
				if x >= 区域[1] and x <= 区域[3] and y >= 区域[2] and y <= 区域[4] then
					table.insert(已领取设备 , 设备名称)
					break
				end
			end
		end
		
		if 找到 then
			tap(click_x , click_y) -- 统一在这里点击
			领取次数 = 领取次数 + 1
			显示信息("通过["..find_method.."]找到，点击领取第"..领取次数.."次")
			延迟(2000) -- 增加延迟确保UI响应
			
			-- 处理可能出现的奖励领取弹窗
			local 找到奖励 , _ , _ = 找图_区域找图(513 , 28 , 779 , 137 , "特勤处_点击获取奖励.png" , "101010" , 0.9 , 0 , true)
			if 找到奖励 then
				显示信息("跳过奖励领取")
				延迟(1500)
			end
		else
			if 领取次数 > 0 then
				显示信息("已领取 "..领取次数.." 个完成的制造项目")
			else
				显示信息("没有发现需要领取的制造项目")
			end
			延迟(1000)
			break
		end
	end
	
	-- 如果达到最大领取次数，可能还有更多项目需要领取
	if 领取次数 >= 最大领取次数 then
		显示信息("警告：已达到最大领取次数限制，可能还有更多项目未领取，尝试自动出售...")
		if _G.执行智能出售流程 then
			local ok , result = pcall(_G.执行智能出售流程)
			if ok and result then
				显示信息("自动出售完成，继续制造流程")
			else
				显示信息("自动出售未成功，稍后重试")
			end
		else
			显示信息("未找到自动出售流程函数，无法自动出售")
		end
		return -- 结束当前领取流程，等待出售完成
	end
	
	-- 新增：如果有领取到设备，立即尝试启动新的制造
	if #已领取设备 > 0 then
		显示信息("检测到"..#已领取设备.."个设备已领取，准备启动新的制造...")
		-- 确保返回到特勤处主界面
		打开特勤处()
		延迟(2000)
		-- 对每个已领取的设备启动新的制造
		for _ , 设备名称 in ipairs(已领取设备) do
			显示信息("正在为设备 '"..设备名称.."' 启动新的制造...")
			local 坐标 = 设备坐标[设备名称]
			if 坐标 then
				tap(坐标.x , 坐标.y)
				延迟(2000)
				特勤处_通用制造(设备名称)
			end
		end
	end
end
--特勤处出现点击兑换后续制造流程
function 军需处兑换流程()
	延迟(3000)
	
	-- 步骤1：先点击一键补齐
	local 找到补齐 , x , y = 找图_区域找图(955 , 622 , 1279 , 717 , "特勤处_一键补齐.png" , "151515" , 0.9 , 0 , true)
	if 找到补齐 then
		显示信息("发现并点击'一键补齐'")
		延迟(2000)
		特勤处_购买() -- 调用购买流程
		延迟(500)
	else
		local 点击补齐颜色 , x , y = 找色_区域多点找色(896 , 600 , 1279 , 719 , "11cc7f" , "1|-4|12b271|25|-1|11cc7f" , 0 , 0.9 , true)
		延迟(2000)
		特勤处_购买()
	end
	-- 步骤2: 寻找并点击"兑换"按钮
	local 找到兑换 , x , y = 找图_区域找图(896 , 583 , 1233 , 690 , "特勤处_兑换.png|特勤处_兑换1.png" , "151515" , 0.9 , 0 , true)
	if 找到兑换 then
		显示信息("发现并点击'兑换'")
		延迟(1500)
	else
		显示信息("未找到兑换按钮")
	end
	local 确认兑换 , x , y = 找图_区域找图(644 , 454 , 958 , 535 , "特勤处_确认补齐.png" , "151515" , 0.9 , 0 , true)
	if 确认兑换 then
		显示信息("确认补齐")
		延迟(1000)

		-- 【新增】确认补齐后的保险机制：固定坐标返回
		显示信息("确认补齐完成，执行保险返回操作")
		点击(73, 32)  -- 固定坐标返回
		延迟(1500)
	end
	-- 步骤3: 最后检查是否需要返回
	local 返回军需处 , x , y = 找图_区域找图(0 , 0 , 158 , 61 , "特勤处_点击军需处.png" , "151515" , 0.9 , 0 , true)
	if 返回军需处 then
		显示信息("兑换完成返回军需处")
		延迟(1500)
	end
end
-- 通用制造函数，替代原来的四个重复函数
function 特勤处_通用制造(设备名称)
	local 返回按钮配置 = {
		技术中心 = {
			图片 = "特勤处_返回技术中心.png" ,
			区域 = {0 , 0 , 200 , 64} ,
			颜色 = "151515"
		} ,
		工作台 = {
			图片 = "特勤处_返回工作台.png" ,
			区域 = {0 , 0 , 200 , 77} ,
			颜色 = "101010"
		} ,
		制药台 = {
			图片 = "特勤处_返回制药台.png" ,
			区域 = {0 , 0 , 200 , 77} ,
			颜色 = "101010"
		} ,
		防具台 = {
			图片 = "特勤处_返回防具台.png" ,
			区域 = {0 , 0 , 155 , 61} ,
			颜色 = "101010"
		}
	}

	延迟(1000)
	local 生产成功 = false
	for i = 1 , 3 do -- 最多尝试3轮补齐->生产
		-- 步骤1：点击置顶装备（如果存在）
		local 找到置顶 , _ , _ = 找图_区域找图(388 , 152 , 443 , 201 , "特勤处_查找置顶.png" , "202020" , 0.9 , 0 , true)
		if 找到置顶 then
			显示信息("点击置顶装备" , false)
			延迟(1000)
		end
		
		-- 步骤2: 点击购物车和前去兑换
		local 找到购物车 , x , y = 找图_区域找图(698 , 558 , 785 , 619 , "特勤处_寻找购物车.png" , "151515" , 0.9 , 0 , true)
		if 找到购物车 then
			显示信息("点击购物车")
			延迟(2000)
		end

		-- 使用用户提供的更可靠的 "前去兑换" 查找方式
		local 找到兑换 , _ , _ = 找图_区域找图(973 , 144 , 1204 , 234 , "特勤处_前去兑换.png|特勤处_前去兑换1.png" , "202020" , 0.8 , 0 , true)
		if 找到兑换 then
			显示信息("前去兑换")
			延迟(1500)
			军需处兑换流程()
		else
			local 找到兑换颜色 , x , y = 找色_区域多点找色(994 , 168 , 1187 , 225 , "11cc7f" , "1|-4|12b271|25|-1|11cc7f" , 0 , 0.9 , true)
			延迟(2000)
			军需处兑换流程()
		end
		
		-- 步骤3：尝试点击生产
		local 找到生产 , _ , _ = 找图_区域找图(955 , 622 , 1279 , 717 , "特勤处_生产.png" , "151515" , 0.9 , 0 , true)
		if 找到生产 then
			显示信息("发现并点击'生产'")
			延迟(4000) -- 增加延迟以等待UI响应
			-- 验证是否开始生产
			local 找到终止 , _ , _ = 找图_区域找图(956 , 618 , 1279 , 714 , "特勤处_找到终止.png|特勤处_找到终止1.png" , "151515" , 0.9 , 0)
			if 找到终止 then
				显示信息(string.format('✓ %s: 制造已开始，准备返回特勤处主界面...' , 设备名称))
				生产成功 = true
				break -- 生产成功，跳出循环
			end
		end
		
		显示信息(string.format("第%d轮尝试未能开始生产，2秒后重试..." , i))
		sleep(2000)
	end
	
	if 生产成功 then
		-- 根据设备类型使用对应的返回按钮配置
		local 返回配置 = 返回按钮配置[设备名称]
        local max_return_attempts = 5
        local return_attempt = 0
        local returned_to_hub = false

        while return_attempt < max_return_attempts do
            return_attempt = return_attempt + 1
			显示信息(string.format("尝试返回特勤处主界面... (%d/%d)" , return_attempt , max_return_attempts))

            -- 检查是否已在主界面
			local is_at_hub , _ , _ = 找图_区域找图(0 , 0 , 181 , 64 , "特勤处_返回特勤处大厅.png" , "101010" , 0.9 , 0)
            if is_at_hub then
                显示信息("✓ 已成功返回特勤处主界面。")
                returned_to_hub = true
                break
            end

            -- 如果不在主界面，则尝试返回
            if 返回配置 then
				local returned , _ , _ = 找图_区域找图(
				返回配置.区域[1] , 返回配置.区域[2] ,
				返回配置.区域[3] , 返回配置.区域[4] ,
				返回配置.图片 , 返回配置.颜色 , 0.9 , 0 , true
                )
                if not returned then
                    显示信息("...特定返回按钮未找到，尝试通用返回...")
					tap(91 , 31)
                end
            else
                显示信息("警告：未找到设备对应的返回按钮配置，使用通用返回")
				tap(91 , 31)
            end
            延迟(2500)
        end
        
        if not returned_to_hub then
            显示信息("❌ 尝试多次后未能返回特勤处主界面，将强制重新打开。")
            打开特勤处()
        end
	else
		显示信息(string.format("❌ %s: 经过多轮尝试后制造失败。" , 设备名称))
	end
end

-- 为了保持向后兼容，保留原函数名但改为调用通用函数
function 特勤处_制造_技术中心()
	特勤处_通用制造("技术中心")
end

function 特勤处_制造_工作台()
	特勤处_通用制造("工作台")
end

function 特勤处_制造_制药台()
	特勤处_通用制造("制药台")
end

function 特勤处_制造_防具台()
	特勤处_通用制造("防具台")
end

-- 解析时间字符串为分钟数
function 解析剩余时间(时间字符串)
    if not 时间字符串 then return 0 end
    -- 移除所有空格
	时间字符串 = string.gsub(时间字符串 , "%s+" , "")
    -- 尝试匹配时分格式
	local 小时 , 分钟 = string.match(时间字符串 , "(%d+):(%d+)")
    if 小时 and 分钟 then
        return tonumber(小时) * 60 + tonumber(分钟)
    end
    -- 如果只有分钟
	local 纯分钟 = string.match(时间字符串 , "(%d+)")
    if 纯分钟 then
        return tonumber(纯分钟)
    end
    return 0
end

-- 将分钟数转换为可读时间格式
function 格式化时间(总分钟)
    if 总分钟 <= 0 then return "0分钟" end
    local 小时 = math.floor(总分钟 / 60)
    local 分钟 = 总分钟 % 60
    if 小时 > 0 then
		return string.format("%d小时%d分钟" , 小时 , 分钟)
    else
		return string.format("%d分钟" , 分钟)
    end
end

-- 检查设备状态
function 检查设备状态(区域)
	local 找到 , _ , _ = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_查询是否空闲.png" , "202020" , 0.9 , 0)
    return 找到
end

-- 【修复】获取防具台剩余时间（使用正确坐标）
function 获取防具台时间()
    打开特勤处()
    延迟(1500)
	local 时间文本 = OCR功能.ocr_时间(969, 574, 1151, 635)
    return 解析剩余时间(时间文本)
end

-- 等待并提示剩余时间
function 等待并提示(等待时间)
    local 开始时间 = os.time()
    local 结束时间 = 开始时间 + 等待时间 * 60
    local 上次提示时间 = 0
    
    while os.time() < 结束时间 do
        local 当前时间 = os.time()
        local 剩余秒数 = 结束时间 - 当前时间
        local 剩余分钟 = math.floor(剩余秒数 / 60)
        local 剩余秒 = 剩余秒数 % 60
        
        -- 每30秒显示一次剩余时间
        if 当前时间 - 上次提示时间 >= 30 then
			显示信息(string.format("距离下次检查还有%d分%d秒" , 剩余分钟 , 剩余秒))
            上次提示时间 = 当前时间
        end
        
		sleep(1000) -- 每秒检查一次
    end
end

-- 错误处理相关常量和变量
local 最大重试次数 = 3
local 重试等待时间 = 2000 -- 毫秒

-- 状态反馈增强版显示信息函数
function 显示详细信息(步骤名称 , 状态类型 , 消息 , 显示Toast)
    local 状态图标 = {
		开始 = "🔄" ,
		成功 = "✅" ,
		失败 = "❌" ,
		警告 = "⚠️" ,
		重试 = "🔁" ,
		等待 = "⏳" ,
        完成 = "🎯"
    }
    
    local 图标 = 状态图标[状态类型] or "ℹ️"
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
	local 完整消息 = string.format("[%s] %s [%s] %s" , 时间戳 , 图标 , 步骤名称 , 消息)
    
    print(完整消息)
	
	-- 只在重要状态变化时显示toast
	if 显示Toast ~= false and (状态类型 == "失败" or 状态类型 == "成功" or 状态类型 == "完成") then
        toast(完整消息)
    end
	
	-- 记录到日志系统
	if Logger then
		if 状态类型 == "失败" then
			Logger.error("特勤处制造" , 完整消息)
		elseif 状态类型 == "警告" then
			Logger.warn("特勤处制造" , 完整消息)
		else
			Logger.info("特勤处制造" , 完整消息)
		end
	end
	
    return true
end

-- 带重试机制的操作执行函数
function 执行带重试(操作名称 , 执行函数 , ...)
    local 当前重试次数 = 0
    while 当前重试次数 < 最大重试次数 do
		显示详细信息(操作名称 , "开始" , string.format("第%d次尝试" , 当前重试次数 + 1))
        
		local 成功 , 结果 = pcall(执行函数 , ...)
        if 成功 and 结果 ~= nil then -- 修改：确保有明确的返回值
			显示详细信息(操作名称 , "成功" , "操作成功完成")
			return true , 结果
        end
        
        当前重试次数 = 当前重试次数 + 1
        if 当前重试次数 < 最大重试次数 then
			显示详细信息(操作名称 , "重试" , string.format("操作失败，%d秒后重试..." , 重试等待时间/1000))
            sleep(重试等待时间)
        else
			显示详细信息(操作名称 , "失败" , "达到最大重试次数，操作失败")
			return false , nil
        end
    end
	return false , nil
end

-- 【自动挂机专用】单轮快速制造函数
function 智能特勤处制造(空闲设备列表)
	显示详细信息("特勤处制造" , "开始" , "开始单轮特勤处检查与制造流程...")
	
	-- 定义制造函数映射
	local 制造函数映射 = {
		技术中心 = 特勤处_制造_技术中心 ,
		工作台 = 特勤处_制造_工作台 ,
		制药台 = 特勤处_制造_制药台 ,
		防具台 = 特勤处_制造_防具台 ,
	}
	
	-- 进入特勤处并领取已完成
	打开特勤处()
	延迟(2000) -- 确保界面完全加载
	
	-- 检查并领取所有完成的项目
	local function 检查并领取完成项目()
		local 找到完成 , _ , _ = 找图_区域找图(142 , 479 , 1265 , 653 , "特勤处_制造完成1.png|特勤处_制造完成.png" , "050505" , 0.9 , 0)
		if 找到完成 then
			特勤处_制造完毕()
			显示详细信息("特勤处制造" , "导航" , "领取完成，返回特勤处主界面..." , false)
			打开特勤处()
			return true
		end
		return false
	end
	
	-- 首次检查完成项目
	检查并领取完成项目()
	延迟(1000) -- 领取完后稍作等待

	-- 依次处理已知的空闲设备
	if not 空闲设备列表 or #空闲设备列表 == 0 then
		显示详细信息("特勤处制造" , "跳过" , "没有需要启动制造的空闲设备。" , false)
	else
		-- 调试信息：输出空闲设备列表
		local 设备列表文本 = ""
		for _ , 名称 in ipairs(空闲设备列表) do
			设备列表文本 = 设备列表文本 .. 名称 .. ", "
		end
		显示详细信息("特勤处制造" , "信息" , string.format("发现%d个空闲设备: %s" , #空闲设备列表 , 设备列表文本) , false)
		
		for i , 设备名称 in ipairs(空闲设备列表) do
			显示详细信息("特勤处制造" , "处理" , string.format("正在处理第%d个空闲设备: %s" , i , 设备名称) , false)
			
			-- 每次处理新设备前，先检查是否有完成的项目
			检查并领取完成项目()
			延迟(1000)
			
			-- 直接使用固定坐标点击
			local 坐标 = 设备坐标[设备名称]
			if 坐标 then
				显示详细信息("特勤处制造" , "点击" , string.format("点击设备坐标 x=%d, y=%d" , 坐标.x , 坐标.y) , false)
				tap(坐标.x , 坐标.y)
				延迟(4000) -- 等待UI响应
				
				-- 【修复】增强制造启动逻辑
				local 制造函数 = 制造函数映射[设备名称]
				if 制造函数 then
					显示详细信息("特勤处制造" , "启动" , string.format("开始为设备 '%s' 启动制造..." , 设备名称) , false)
					local 制造成功 = false
					
					-- 尝试启动制造
					local ok , result = pcall(制造函数)
					if ok then
						-- 验证制造是否成功启动
						延迟(2000) -- 等待制造界面稳定
						local 找到终止 , _ , _ = 找图_区域找图(956 , 618 , 1279 , 714 , "特勤处_找到终止.png|特勤处_找到终止1.png" , "151515" , 0.9 , 0)
						if 找到终止 then
							显示详细信息("特勤处制造" , "成功" , string.format("设备 '%s' 制造已成功启动" , 设备名称) , false)
							制造成功 = true
						else
							-- 【新增】备用验证方法
							local 备用终止 , _ , _ = 找图_区域找图(956 , 618 , 1279 , 714 , "特勤处_找到终止2.png|特勤处_找到终止3.png" , "151515" , 0.8 , 0)
							if 备用终止 then
								显示详细信息("特勤处制造" , "成功" , string.format("设备 '%s' 制造已成功启动（备用验证）" , 设备名称) , false)
								制造成功 = true
							else
								显示详细信息("特勤处制造" , "警告" , string.format("设备 '%s' 制造启动状态未知" , 设备名称) , false)
							end
						end
					else
						显示详细信息("特勤处制造" , "错误" , string.format("设备 '%s' 制造启动失败: %s" , 设备名称 , tostring(result)) , false)
					end
					
					if not 制造成功 then
						显示详细信息("特勤处制造" , "警告" , string.format("设备 '%s' 制造可能未成功启动，将在下次检查时重试" , 设备名称) , false)
					end
				else
					显示详细信息("特勤处制造" , "错误" , string.format("未找到设备 '%s' 对应的制造函数" , 设备名称) , false)
				end
			
				-- 处理完一个设备后，确保返回特勤处主界面
				if i < #空闲设备列表 then
					显示详细信息("特勤处制造" , "导航" , "处理完一个设备，确保返回特勤处主界面..." , false)
					local 找到返回 , _ , _ = 找图_区域找图(0 , 0 , 181 , 64 , "特勤处_返回特勤处大厅.png" , "101010" , 0.9 , 0)
					if not 找到返回 then
						显示详细信息("特勤处制造" , "导航" , "点击返回按钮返回特勤处主界面..." , false)
						tap(91 , 31) -- 通用返回按钮位置
						延迟(3000) -- 等待返回动画完成
					
						-- 再次检查是否返回到特勤处主界面
						找到返回 , _ , _ = 找图_区域找图(0 , 0 , 181 , 64 , "特勤处_返回特勤处大厅.png" , "101010" , 0.9 , 0)
						if not 找到返回 then
							显示详细信息("特勤处制造" , "警告" , "可能未能返回特勤处主界面，尝试重新进入特勤处..." , false)
							打开特勤处()
							延迟(2000)
						end
					else
						显示详细信息("特勤处制造" , "导航" , "已在特勤处主界面，继续下一个设备..." , false)
					end
				end
			else
				显示详细信息("特勤处制造" , "错误" , string.format("未找到设备'%s'的坐标定义" , 设备名称) , false)
			end
		end
	end
	
	-- 最后再检查一次是否有完成的项目
	检查并领取完成项目()
	
	显示详细信息("特勤处制造" , "流程结束" , "单轮制造任务已检查完毕。" , false)
	
	-- 核心修复：确保制造流程结束后，返回游戏大厅，为下一次状态检查提供统一的环境
	显示详细信息("导航" , "返回大厅" , "制造流程结束后，返回游戏大厅。")
	工具函数.尝试返回大厅("智能制造完成")
end

-- 获取所有设备状态函数 - 修复版
function 获取所有设备状态()
    -- 【关键修复】节能模式检查
    if _G.State and _G.State.isGameClosed then
        显示信息("💤 节能模式下跳过设备状态获取", false)
        return false, nil, 0, {}
    end

    -- 【修复问题1】OCR操作期间启用静默模式
    local OCR静默模式 = true

    -- 先返回大厅，确保状态一致
    工具函数.尝试返回大厅("准备获取设备状态")
    延迟(1500)

    打开特勤处()
    延迟(1500)

    local 所有剩余时间 = {}
    local 制造中数量 = 0
    local 空闲设备 = {}
    local 完成设备 = {}
    
	for 设备名称 , 坐标 in pairs(设备时间坐标) do
        -- 【修复】先检查是否有时间，添加调试信息
		local 时间文本 = OCR功能.ocr_时间(坐标[1] , 坐标[2] , 坐标[3] , 坐标[4])
        local 剩余分钟 = 解析剩余时间(时间文本)

        -- 【调试】输出OCR识别结果
        if not OCR静默模式 then
            显示信息(string.format("🔍 设备 '%s' OCR结果: 时间文本='%s', 剩余分钟=%s, 坐标=[%d,%d,%d,%d]",
                设备名称, tostring(时间文本), tostring(剩余分钟), 坐标[1], 坐标[2], 坐标[3], 坐标[4]))
        end
        
        if 剩余分钟 and 剩余分钟 > 0 then
            -- 有时间，说明正在制造
            -- 【修复问题1】OCR操作期间使用静默模式
            if not OCR静默模式 then
                显示信息(string.format("设备 '%s' 正在制造中，剩余时间: %s" , 设备名称 , 格式化时间(剩余分钟)))
            end
			table.insert(所有剩余时间 , 剩余分钟)
            制造中数量 = 制造中数量 + 1
        else
            -- 没有时间，检查是否是空闲状态
            local 区域 = 设备状态区域[设备名称]

            -- 【调试】输出区域坐标信息
            if not OCR静默模式 then
                显示信息(string.format("🔍 [调试] 设备 '%s' 检测区域: [%d,%d,%d,%d]",
                    设备名称, 区域[1], 区域[2], 区域[3], 区域[4]))
            end

			local 找到空闲 , x1 , y1 = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_查询是否空闲.png" , "202020" , 0.9 , 0)

            -- 【调试】输出空闲检测结果
            if not OCR静默模式 then
                显示信息(string.format("🔍 [调试] 设备 '%s' 空闲检测: 找到=%s, 坐标=(%s,%s)",
                    设备名称, tostring(找到空闲), tostring(x1), tostring(y1)))
            end

            if 找到空闲 then
                -- 找到空闲图标，说明是空闲状态
                if not OCR静默模式 then
                    显示信息(string.format("设备 '%s' 处于空闲状态" , 设备名称))
                end
				table.insert(空闲设备 , 设备名称)
            else
                -- 【修复】增强空闲状态检测 - 降低相似度阈值
                local 找到空闲备用1 , x2 , y2 = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_查询是否空闲1.png|特勤处_查询是否空闲2.png" , "202020" , 0.8 , 0)

                -- 【调试】输出备用空闲检测结果
                if not OCR静默模式 then
                    显示信息(string.format("🔍 [调试] 设备 '%s' 备用空闲检测: 找到=%s, 坐标=(%s,%s)",
                        设备名称, tostring(找到空闲备用1), tostring(x2), tostring(y2)))
                end

                if 找到空闲备用1 then
                    if not OCR静默模式 then
                        显示信息(string.format("设备 '%s' 处于空闲状态（备用检测）" , 设备名称))
                    end
                    table.insert(空闲设备 , 设备名称)
                else
                    -- 【新增】尝试更宽松的空闲检测
                    local 找到空闲宽松 , x3 , y3 = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_查询是否空闲.png" , "303030" , 0.7 , 0)

                    -- 【调试】输出宽松空闲检测结果
                    if not OCR静默模式 then
                        显示信息(string.format("🔍 [调试] 设备 '%s' 宽松空闲检测: 找到=%s, 坐标=(%s,%s)",
                            设备名称, tostring(找到空闲宽松), tostring(x3), tostring(y3)))
                    end

                    if 找到空闲宽松 then
                        if not OCR静默模式 then
                            显示信息(string.format("设备 '%s' 处于空闲状态（宽松检测）" , 设备名称))
                        end
                        table.insert(空闲设备 , 设备名称)
                    else
                        -- 检查是否完成
                        local 找到完成 , x4 , y4 = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_制造完成1.png|特勤处_制造完成.png" , "050505" , 0.9 , 0)

                        -- 【调试】输出完成检测结果
                        if not OCR静默模式 then
                            显示信息(string.format("🔍 [调试] 设备 '%s' 完成检测: 找到=%s, 坐标=(%s,%s)",
                                设备名称, tostring(找到完成), tostring(x4), tostring(y4)))
                        end

                        if 找到完成 then
                            if not OCR静默模式 then
                                显示信息(string.format("设备 '%s' 制造完成，等待领取" , 设备名称))
                            end
                            table.insert(完成设备 , 设备名称)
                        else
                            -- 【修复】增强完成状态检测
                            local 找到完成备用 , x5 , y5 = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_制造完成2.png|特勤处_制造完成3.png" , "050505" , 0.8 , 0)

                            -- 【调试】输出备用完成检测结果
                            if not OCR静默模式 then
                                显示信息(string.format("🔍 [调试] 设备 '%s' 备用完成检测: 找到=%s, 坐标=(%s,%s)",
                                    设备名称, tostring(找到完成备用), tostring(x5), tostring(y5)))
                            end

                            if 找到完成备用 then
                                显示信息(string.format("设备 '%s' 制造完成，等待领取（备用检测）" , 设备名称))
                                table.insert(完成设备 , 设备名称)
                            else
                                -- 【警告】所有检测都失败，可能是界面问题或图片缺失
                                显示信息(string.format("⚠️ 设备 '%s' 状态识别失败 - 所有检测方法都未找到匹配项" , 设备名称))
                                显示信息(string.format("   建议检查: 1)界面是否正常 2)图片文件是否完整 3)设备区域坐标是否准确"))

                                -- 【临时方案】假设为空闲状态，避免误判为完成
                                显示信息(string.format("   临时处理: 将设备 '%s' 标记为空闲状态" , 设备名称))
                                table.insert(空闲设备 , 设备名称)
                            end
                        end
                    end
                end
            end
        end
    end

    -- 处理所有制造完成的设备
    if #完成设备 > 0 then
		显示信息(string.format("检测到 %d 个制造完成的设备，开始领取..." , #完成设备),false)
        特勤处_制造完毕() -- 调用统一的领取函数
        显示信息("制造完成的设备领取完毕。")
        
        -- 重要：重新检查一次，确保所有设备都被正确领取
        打开特勤处()
        延迟(2000)
        
        -- 再次检查是否有遗漏的完成设备
		for 设备名称 , 区域 in pairs(设备状态区域) do
			local 找到完成 , _ , _ = 找图_区域找图(区域[1] , 区域[2] , 区域[3] , 区域[4] , "特勤处_制造完成1.png|特勤处_制造完成.png" , "050505" , 0.9 , 0)
            if 找到完成 then
				显示信息(string.format("发现遗漏的完成设备：%s，再次领取" , 设备名称))
                特勤处_制造完毕()
                break
            end
        end
        
        -- 将所有已领取的设备添加到空闲列表
		for _ , 设备名称 in ipairs(完成设备) do
            -- 检查是否已经在空闲列表中
            local 已存在 = false
			for _ , 空闲名称 in ipairs(空闲设备) do
                if 空闲名称 == 设备名称 then
                    已存在 = true
                    break
                end
            end
            if not 已存在 then
				table.insert(空闲设备 , 设备名称)
				显示信息(string.format("设备 '%s' 已领取并标记为空闲。" , 设备名称))
            end
        end
    end

    local 是否全部在制造 = (制造中数量 == 4)
    local 最短剩余时间 = nil

    if #所有剩余时间 > 0 then
        最短剩余时间 = math.min(table.unpack(所有剩余时间))
    end

    -- 【修复问题1&2】OCR操作完成后显示状态汇总
    显示信息(string.format("📊 设备状态汇总: 制造中=%d, 空闲=%d, 完成=%d, 全部在制造=%s",
        制造中数量, #空闲设备, #完成设备, 是否全部在制造 and "是" or "否"))

    if #空闲设备 > 0 then
        local 空闲设备名单 = table.concat(空闲设备, ", ")
        显示信息(string.format("🔧 检测到空闲设备: %s", 空闲设备名单))
    end

    -- 【修复问题2】确保逻辑正确：如果有空闲设备，不应该返回"全部在制造"
    if #空闲设备 > 0 then
        是否全部在制造 = false
        显示信息("⚠️ 修正逻辑：检测到空闲设备，设置全部在制造=false")
    end

    -- 返回四个关键信息：是否全部在制造，最早一个完工的设备需要的时间, 制造中的数量, 以及空闲设备列表
	return 是否全部在制造 , 最短剩余时间 , 制造中数量 , 空闲设备
end

-- 【修复】获取特勤处制造时间文本（使用正确坐标）
function 获取特勤处制造时间文本()
	return OCR功能.ocr_时间(969, 574, 1151, 635)
end

-- 清理OCR缓存
function 清理OCR缓存()
	collectgarbage("collect") -- 使用Lua的垃圾回收机制清理OCR缓存
	显示信息("OCR缓存已清理。")
end

--没有做
-- 检查是否所有工作台都在制造中
function 检查是否全部在制造()
    -- 使用制造完成图片检查工作台状态
	local 找到 , x , y = 找图_区域找图(142 , 479 , 1265 , 653 , "特勤处_制造完成1.png|特勤处_制造完成.png" , "050505" , 0.9 , 0)
    -- 如果找到制造完成图片，说明有空闲工作台
    -- 如果没找到，说明都在制造中
    return not 找到
end

-- 检查设备是否正在制造（通过OCR识别时间）
function 检查设备制造状态(设备名称)
	local 坐标 = 设备坐标[设备名称]
	if not 坐标 then
		显示信息("错误：未知的设备名称：" .. 设备名称)
		return false
	end
	
	-- OCR识别时间区域（坐标上方约100像素的区域）
	local 时间文本 = OCR功能.ocr_start(坐标.x - 50 , 坐标.y - 100 , 坐标.x + 50 , 坐标.y - 20)
	
	-- 如果能识别到时间格式（比如"XX:XX"），说明正在制造
	if 时间文本 and string.match(时间文本 , "%d+:%d+") then
		显示信息(设备名称 .. " 正在制造中，剩余时间: " .. 时间文本)
		return "制造中"
	end
	
	-- 检查是否空闲（通过特定颜色或图片）
	local index , x , y = findPic(坐标.x - 30 , 坐标.y - 30 , 坐标.x + 30 , 坐标.y + 30 , "特勤处_空闲.png|特勤处_空闲1.png" , "101010" , 0 , 0.7)
	if x > - 1 then
		显示信息(设备名称 .. " 处于空闲状态")
		return "空闲"
	end
	
	-- 如果既没有时间，也不是空闲，则认为是制造完成
	显示信息(设备名称 .. " 制造完成，等待领取")
	return "完成"
end

-- 处理制造完成的设备
function 处理制造完成(设备名称)
	local 坐标 = 设备坐标[设备名称]
	if not 坐标 then return false end
	
	显示信息("处理 " .. 设备名称 .. " 的制造完成状态")
	
	-- 点击设备区域
	tap(坐标.x , 坐标.y)
	延迟(1500) -- 增加延迟确保UI响应
	
	-- 检查并处理奖励界面
	local 找到奖励 , _ , _ = 找图_区域找图(526 , 34 , 777 , 137 , "特勤处_点击获取奖励.png" , "202020" , 0.8 , 0 , true)
	if 找到奖励 then
		显示信息("跳过奖励领取")
		延迟(1500)
	end
	
	-- 确保返回到特勤处主界面
	local 找到特勤处大厅 , _ , _ = 找图_区域找图(0 , 0 , 181 , 64 , "特勤处_返回特勤处大厅.png" , "101010" , 0.9 , 0)
	if not 找到特勤处大厅 then
		显示信息("返回特勤处主界面...")
		tap(91 , 31) -- 通用返回按钮位置
		延迟(1500)
	end
	
	return true
end

-- 导出模块
return {
    -- 自动挂机功能
	智能特勤处制造 = 智能特勤处制造 ,
	获取所有设备状态 = 获取所有设备状态 ,

	-- 【修复】添加主脚本需要的函数别名
	为设备列表启动制造 = 智能特勤处制造 ,

    -- 公共工具
	格式化时间 = 格式化时间 ,
    打开特勤处 = 打开特勤处 ,
    
    -- 【新增】OCR相关函数
    清理OCR缓存 = 清理OCR缓存,
    获取特勤处制造时间文本 = 获取特勤处制造时间文本,

	-- 【新增】仓库整理功能
	执行仓库整理 = 执行仓库整理
}
