--[[
模块：日志系统重构版 (Logger)
功能：基于懒人精灵文件功能的完整日志记录系统
特点：
- 支持多级别日志 (DEBUG, INFO, WARN, ERROR, FATAL)
- 自动创建日志目录和文件
- 使用懒人精灵原生文件操作函数
- 智能日志轮转 (基于文件大小)
- 支持结构化数据记录
- 安全的路径处理和错误处理
]]

local Logger = {}

-- 日志级别定义
Logger.LEVEL = {
    FATAL = 0,
    ERROR = 1,
    WARN = 2,
    INFO = 3,
    DEBUG = 4,
    VERBOSE = 5
}

-- 输出级别定义（全局）
_G.OUTPUT_LEVEL = {
    NONE = 0,
    ERROR = 1,
    WARN = 2,
    INFO = 3,
    DEBUG = 4,
    VERBOSE = 5
}

-- 日志级别名称映射
local LEVEL_NAMES = {
    [Logger.LEVEL.FATAL] = "致命",
    [Logger.LEVEL.ERROR] = "错误",
    [Logger.LEVEL.WARN] = "警告",
    [Logger.LEVEL.INFO] = "信息",
    [Logger.LEVEL.DEBUG] = "调试",
    [Logger.LEVEL.VERBOSE] = "详细"
}

-- 日志级别图标映射
local LEVEL_ICONS = {
    [Logger.LEVEL.FATAL] = "💀",
    [Logger.LEVEL.ERROR] = "❌",
    [Logger.LEVEL.WARN] = "⚠️",
    [Logger.LEVEL.INFO] = "✓",
    [Logger.LEVEL.DEBUG] = "🔍",
    [Logger.LEVEL.VERBOSE] = "📝"
}

-- 获取SD卡路径
local function getSafeSDPath()
    -- 直接返回固定路径
    return "/storage/emulated/0/Download"
end

-- 检查并创建备用日志路径
local function getBackupLogDir()
    -- 此函数已不再使用，但保留为空函数以避免潜在的引用错误
    return "/storage/emulated/0/Download/日志"
end

-- 日志配置
Logger.config = {
    logDir = "/storage/emulated/0/Download/日志",
    logFile = "日志.txt",
    level = Logger.LEVEL.INFO,
    maxBufferSize = 1000,
    maxFileSize = 1024 * 1024 * 2, -- 2MB
    maxBackups = 5,
    flushInterval = 5,
    enableConsole = true,
    dateFormat = "%Y-%m-%d %H:%M:%S"
}

-- 计算完整日志路径
local function getFullLogPath()
    return Logger.config.logDir .. "/" .. Logger.config.logFile
end

-- 生成备份文件路径
local function getBackupLogPath(backupNumber)
    local logFile = Logger.config.logFile
    local logDir = Logger.config.logDir

    -- 从 "日志.txt" 提取文件名和扩展名
    local fileName, extension = logFile:match("^(.+)%.([^%.]+)$")
    if not fileName or not extension then
        -- 如果没有扩展名，直接在文件名后加数字
        return logDir .. "/" .. logFile .. backupNumber .. ".txt"
    end

    -- 生成格式：日志1.txt, 日志2.txt, 日志3.txt
    return logDir .. "/" .. fileName .. backupNumber .. "." .. extension
end

-- 会话统计信息
local stats = {
    linesWritten = 0,
    errors = 0,
    warnings = 0,
    sessionStart = os.time(),
    totalFlushes = 0,
    bufferOverflows = 0
}

local log_buffer = {}     -- 日志缓冲区
local lastFlushTime = os.time()
local isInitialized = false

-- =================== 核心工具函数 ===================

-- 安全的JSON编码（简化版）
local function safeJsonEncode(data)
    if type(data) ~= "table" then
        return tostring(data)
    end
    
    local result = {}
    for k, v in pairs(data) do
        local key = '"' .. tostring(k) .. '"'
        local value
        if type(v) == "string" then
            value = '"' .. v:gsub('"', '\\"') .. '"'
        elseif type(v) == "number" or type(v) == "boolean" then
            value = tostring(v)
        elseif type(v) == "table" then
            value = safeJsonEncode(v) -- 递归处理
        else
            value = '"' .. tostring(v) .. '"'
        end
        table.insert(result, key .. ":" .. value)
    end
    return "{" .. table.concat(result, ",") .. "}"
end

-- 检查文件是否存在
local function fileExist(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- 获取文件大小（安全版本）
local function getSafeFileSize(filePath)
    if not fileExist(filePath) then
        return 0
    end
    
    local file = io.open(filePath, "r")
    if not file then
        return 0
    end
    
    local size = file:seek("end")
    file:close()
    return size or 0
end

-- 安全读取文件
local function safeReadFile(filePath)
    if not fileExist(filePath) then
        return ""
    end
    
    local file = io.open(filePath, "r")
    if not file then
        return ""
    end
    
    local content = file:read("*a")
    file:close()
    return content or ""
end

-- 确保目录存在的函数
local function ensureDirectoryExists(path)
    -- 使用os.execute创建目录，支持递归创建
    if string.sub(path, -1) == "/" or string.sub(path, -1) == "\\" then
        -- 如果路径以斜杠结尾，去掉它
        path = string.sub(path, 1, -2)
    end
    
    -- 在Windows和Linux上都适用的mkdir命令
    local success = os.execute('mkdir -p "' .. path .. '"')
    if not success then
        -- 尝试使用Windows特定的命令
        success = os.execute('mkdir "' .. path .. '"')
    end
    
    return success
end

-- 安全的文件写入函数
local function safeWriteFile(filePath, content)
    -- 先确保目录存在
    local lastSlash = string.find(string.reverse(filePath), "[/\\]")
    if lastSlash then
        local dirPath = string.sub(filePath, 1, #filePath - lastSlash)
        ensureDirectoryExists(dirPath)
    end
    
    -- 打开文件并写入内容
    local file = io.open(filePath, "w")
    if not file then
        print("❌ 文件写入失败 (无法打开): " .. filePath)
        return false
    end
    
    local success, err = pcall(function()
        file:write(content)
        file:flush()
        file:close()
    end)
    
    if not success then
        print("❌ 文件写入失败 (写入错误): " .. filePath .. " - " .. tostring(err))
        pcall(function() file:close() end)
        return false
    end
    
    return true
end

-- 删除文件
local function delfile(filePath)
    -- 在某些系统上，os.remove可能需要权限
    local success, err = os.remove(filePath)
    if not success then
        print("❌ 文件删除失败: " .. filePath .. " - " .. tostring(err))
    end
    return success
end

-- =================== 日志轮转功能 ===================

-- 日志文件轮转
local function rotateLogFile()
    local logPath = getFullLogPath()
    local currentSize = getSafeFileSize(logPath)
    
    if currentSize < Logger.config.maxFileSize then
        return true -- 不需要轮转
    end
    
    print("🔄 执行日志轮转，当前文件大小: " .. currentSize .. " 字节")
    
    -- 删除最旧的备份文件
    local oldestBackup = getBackupLogPath(Logger.config.maxBackups)
    if fileExist(oldestBackup) then
        delfile(oldestBackup)
    end

    -- 将现有备份文件重命名（倒序）
    for i = Logger.config.maxBackups - 1, 1, -1 do
        local oldBackup = getBackupLogPath(i)
        local newBackup = getBackupLogPath(i + 1)
        if fileExist(oldBackup) then
            -- 读取旧备份文件
            local oldFile = io.open(oldBackup, "r")
            if oldFile then
                local content = oldFile:read("*a")
                oldFile:close()
                
                -- 写入新备份文件
                local newFile = io.open(newBackup, "w")
                if newFile then
                    newFile:write(content)
                    newFile:flush()
                    newFile:close()
                    
                    -- 删除旧备份文件
                    delfile(oldBackup)
                end
            end
        end
    end

    -- 将当前日志文件备份为第1个备份文件
    local firstBackup = getBackupLogPath(1)
    
    -- 读取当前日志文件
    local currentFile = io.open(logPath, "r")
    if currentFile then
        local content = currentFile:read("*a")
        currentFile:close()
        
        -- 写入第一个备份文件
        local backupFile = io.open(firstBackup, "w")
        if backupFile then
            backupFile:write(content)
            backupFile:flush()
            backupFile:close()
            
            -- 清空当前日志文件
            local newFile = io.open(logPath, "w")
            if newFile then
                newFile:close() -- 直接关闭，文件被清空
                print("✓ 日志轮转完成")
                return true
            end
        end
    end
    
    print("❌ 日志轮转失败")
    return false
end

-- =================== 核心日志功能 ===================

-- 内部写日志函数
local function writeLog(level, module, message, data)
    if not isInitialized then
        print("⚠️ 日志系统未初始化，跳过日志记录")
        return
    end
    
    -- 确保level是数字
    local numeric_level = level
    if type(level) == "string" then
        numeric_level = Logger.LEVEL[level:upper()] or Logger.LEVEL.INFO
    end
    
    -- 获取当前日志级别
    local current_level = Logger.config.level
    if type(current_level) == "string" then
        current_level = Logger.LEVEL[current_level:upper()] or Logger.LEVEL.INFO
    end
    
    -- 级别过滤
    if numeric_level > current_level then
        return
    end
    
    -- 构建日志条目
    local timestamp = os.date(Logger.config.dateFormat)
    local levelName = LEVEL_NAMES[numeric_level] or "未知"
    local levelIcon = LEVEL_ICONS[numeric_level] or "❓"
    
    local logEntry = string.format("[%s] %s [%s] [%s] %s", 
                                   timestamp, levelIcon, levelName, module or "系统", message or "")
    
    -- 添加数据部分
    if data then
        local dataStr = safeJsonEncode(data)
        logEntry = logEntry .. " | 数据: " .. dataStr
    end
    
    logEntry = logEntry .. "\n"
    
    -- 【新增】缓冲区溢出保护
    if #log_buffer >= Logger.config.maxBufferSize then
        -- 强制刷新或丢弃最老的日志
        Logger.flush()
        if #log_buffer >= Logger.config.maxBufferSize then
            -- 刷新失败，丢弃最老的50%日志
            local half = math.floor(Logger.config.maxBufferSize / 2)
            for i = 1, half do
                table.remove(log_buffer, 1)
            end
            stats.bufferOverflows = stats.bufferOverflows + 1
            print("⚠️ 日志缓冲区已满且刷新失败，已丢弃 " .. half .. " 条旧日志")
        end
    end

    -- 添加到缓冲区
    table.insert(log_buffer, logEntry)
    
    -- 控制台输出
    if Logger.config.enableConsole then
        print(logEntry:gsub("\n", "")) -- 移除换行符避免重复换行
    end
    
    -- 更新统计
    stats.linesWritten = stats.linesWritten + 1
    if numeric_level == Logger.LEVEL.ERROR or numeric_level == Logger.LEVEL.FATAL then
        stats.errors = stats.errors + 1
    elseif numeric_level == Logger.LEVEL.WARN then
        stats.warnings = stats.warnings + 1
    end
    
    -- 检查是否需要刷新缓冲区
    local currentTime = os.time()
    local shouldFlush = false
    
    -- 立即刷新条件
    if numeric_level <= Logger.LEVEL.ERROR then
        shouldFlush = true -- 错误和致命错误立即刷新
    elseif #log_buffer >= 50 then
        shouldFlush = true -- 缓冲区满了
        stats.bufferOverflows = stats.bufferOverflows + 1
    elseif currentTime - lastFlushTime >= Logger.config.flushInterval then
        shouldFlush = true -- 时间间隔到了
    end
    
    if shouldFlush then
        Logger.flush()
    end
end

-- =================== 公共 API ===================

-- 初始化日志系统
function Logger.init(customConfig)
    -- 合并自定义配置
    if customConfig then
        for k, v in pairs(customConfig) do
            Logger.config[k] = v
        end
    end
    
    -- 强制设置为固定路径
    Logger.config.logDir = "/storage/emulated/0/Download/日志"
    
    print("🔄 初始化日志系统...")
    print("📁 日志目录: " .. Logger.config.logDir)
    print("📄 日志文件: " .. Logger.config.logFile)
    
    -- 确保日志目录存在
    if not ensureDirectoryExists(Logger.config.logDir) then
        print("❌ 日志系统初始化失败：无法创建日志目录")
        return false
    end
    
    local logPath = getFullLogPath()
    print("📝 完整日志路径: " .. logPath)
    
    -- 检查现有日志文件大小，必要时轮转
    if fileExist(logPath) then
        local currentSize = getSafeFileSize(logPath)
        print("📊 现有日志文件大小: " .. currentSize .. " 字节")
        
        if currentSize >= Logger.config.maxFileSize then
            rotateLogFile()
        end
    end
    
    -- 写入会话开始标记
    local sessionHeader = string.format(
        "\n" ..
        "================================\n" ..
        "=== 新的日志会话开始 ===\n" ..
        "时间: %s\n" ..
        "日志级别: %s\n" ..
        "版本: 懒人精灵日志系统 v2.0\n" ..
        "================================\n\n",
        os.date(Logger.config.dateFormat),
        LEVEL_NAMES[Logger.config.level] or "未知"
    )
    
    -- 直接写入文件（不通过缓冲区）
    local file = io.open(logPath, "a")
    if not file then
        -- 尝试创建文件
        file = io.open(logPath, "w")
        if not file then
            print("❌ 日志系统初始化失败：无法创建日志文件")
            return false
        end
    end
    
    local success, err = pcall(function()
        file:write(sessionHeader)
        file:flush()
        file:close()
    end)
    
    if not success then
        print("❌ 日志系统初始化失败：无法写入会话头 - " .. tostring(err))
        return false
    end
    
    isInitialized = true
    stats.sessionStart = os.time()
    
    -- 记录初始化成功日志
    writeLog(Logger.LEVEL.INFO, "Logger", "日志系统初始化成功", {
        level = LEVEL_NAMES[Logger.config.level],
        logPath = logPath,
        maxFileSize = Logger.config.maxFileSize,
        maxBackups = Logger.config.maxBackups
    })
    
    print("✅ 日志系统初始化完成")
    return true
end

-- 不同级别的日志记录函数
function Logger.fatal(module, message, data) 
    writeLog(Logger.LEVEL.FATAL, module, message, data) 
end

function Logger.error(module, message, data) 
    writeLog(Logger.LEVEL.ERROR, module, message, data) 
end

function Logger.warn(module, message, data) 
    writeLog(Logger.LEVEL.WARN, module, message, data) 
end

function Logger.info(module, message, data) 
    writeLog(Logger.LEVEL.INFO, module, message, data) 
end

function Logger.debug(module, message, data) 
    writeLog(Logger.LEVEL.DEBUG, module, message, data) 
end

function Logger.verbose(module, message, data) 
    writeLog(Logger.LEVEL.VERBOSE, module, message, data) 
end

-- 性能记录
function Logger.performance(module, operation, duration, data)
    local perfData = {
        operation = operation,
        duration_ms = duration,
        additional_data = data
    }
    local message = string.format("性能记录: %s 耗时 %.2f ms", operation, duration)
    writeLog(Logger.LEVEL.INFO, module, message, perfData)
end

-- 状态变更记录
function Logger.stateChange(module, fromState, toState, reason)
    local stateData = {
        from = fromState,
        to = toState,
        reason = reason or "未指定原因"
    }
    local message = string.format("状态变更: %s -> %s", fromState, toState)
    writeLog(Logger.LEVEL.DEBUG, module, message, stateData)
end

-- 系统状态快照
function Logger.systemSnapshot(module, customData)
    local snapshot = {
        timestamp = os.time(),
        memory_kb = math.floor(collectgarbage("count")),
        buffer_size = #log_buffer,
        log_lines = stats.linesWritten
    }
    
    -- 合并自定义数据
    if customData then
        for k, v in pairs(customData) do
            snapshot[k] = v
        end
    end
    
    writeLog(Logger.LEVEL.INFO, module, "系统状态快照", snapshot)
end

-- 设置日志级别
function Logger.setLevel(level)
    local oldLevel = Logger.config.level
    
    if type(level) == "string" then
        level = Logger.LEVEL[level:upper()]
    end
    
    if type(level) == "number" and level >= Logger.LEVEL.FATAL and level <= Logger.LEVEL.VERBOSE then
        Logger.config.level = level
        writeLog(Logger.LEVEL.INFO, "Logger", "日志级别已更新", {
            old_level = LEVEL_NAMES[oldLevel],
            new_level = LEVEL_NAMES[level]
        })
    else
        writeLog(Logger.LEVEL.WARN, "Logger", "无效的日志级别，保持当前设置", {
            invalid_level = tostring(level),
            current_level = LEVEL_NAMES[Logger.config.level]
        })
    end
end

-- 将缓冲区中的日志写入文件
function Logger.flush()
    if #log_buffer == 0 then
        return true
    end
    
    local logPath = getFullLogPath()
    
    -- 在轮转之前先检查文件大小
    rotateLogFile()
    
    -- 合并缓冲区内容
    local bufferContent = table.concat(log_buffer)
    
    -- 使用追加模式写入文件
    local file = io.open(logPath, "a")
    if not file then
        -- 尝试创建文件
        file = io.open(logPath, "w")
        if not file then
            print("❌ 日志文件无法打开或创建: " .. logPath)
            writeLog(Logger.LEVEL.ERROR, "Logger", "日志刷新失败 - 无法打开文件", {
                logPath = logPath,
                bufferSize = #log_buffer
            })
            return false
        end
    end
    
    -- 写入内容
    local success, err = pcall(function()
        file:write(bufferContent)
        file:flush() -- 确保数据写入磁盘
        file:close()
    end)
    
    if success then
        log_buffer = {} -- 清空缓冲区
        lastFlushTime = os.time()
        stats.totalFlushes = stats.totalFlushes + 1
        return true
    else
        print("❌ 日志写入失败: " .. tostring(err))
        pcall(function() if file then file:close() end end) -- 尝试关闭文件
        
        writeLog(Logger.LEVEL.ERROR, "Logger", "日志刷新失败 - 写入错误", {
            logPath = logPath,
            bufferSize = #log_buffer,
            error = tostring(err)
        })
        return false
    end
end

-- 获取统计信息
function Logger.getStats()
    local currentStats = {}
    for k, v in pairs(stats) do
        currentStats[k] = v
    end
    
    currentStats.uptime_seconds = os.time() - stats.sessionStart
    currentStats.buffer_size = #log_buffer
    currentStats.log_path = getFullLogPath()
    currentStats.current_log_size = getSafeFileSize(getFullLogPath())
    
    return currentStats
end

-- 清理旧日志文件
function Logger.cleanup(daysToKeep)
    daysToKeep = daysToKeep or 7 -- 默认保留7天
    
    writeLog(Logger.LEVEL.INFO, "Logger", "开始清理旧日志文件", {
        days_to_keep = daysToKeep
    })
    
    local currentTime = os.time()
    local cutoffTime = currentTime - (daysToKeep * 24 * 60 * 60)
    
    local logPath = getFullLogPath()
    local cleaned = 0
    
    -- 清理备份文件
    for i = 1, Logger.config.maxBackups do
        local backupPath = getBackupLogPath(i)
        if fileExist(backupPath) then
            -- 这里应该检查文件的修改时间，但懒人精灵可能没有提供这个功能
            -- 为了安全起见，我们只删除超过最大备份数的文件
            if i > Logger.config.maxBackups then
                if delfile(backupPath) then
                    cleaned = cleaned + 1
                end
            end
        end
    end
    
    writeLog(Logger.LEVEL.INFO, "Logger", "日志清理完成", {
        files_cleaned = cleaned
    })
    
    return cleaned
end

-- 结束日志会话
function Logger.sessionEnd()
    writeLog(Logger.LEVEL.INFO, "Logger", "日志会话即将结束", Logger.getStats())
    
    -- 强制刷新所有缓冲区
    Logger.flush()
    
    -- 写入会话结束标记
    local sessionFooter = string.format(
        "\n" ..
        "================================\n" ..
        "=== 日志会话结束 ===\n" ..
        "结束时间: %s\n" ..
        "会话统计: 共写入 %d 行日志\n" ..
        "错误: %d 次, 警告: %d 次\n" ..
        "缓冲区刷新: %d 次\n" ..
        "================================\n\n",
        os.date(Logger.config.dateFormat),
        stats.linesWritten,
        stats.errors,
        stats.warnings,
        stats.totalFlushes
    )
    
    local logPath = getFullLogPath()
    local existingContent = safeReadFile(logPath)
    safeWriteFile(logPath, existingContent .. sessionFooter)
    
    isInitialized = false
    print("✅ 日志会话已结束，日志文件: " .. logPath)
end

-- 导出日志内容（用于分享或分析）
function Logger.exportLogs(exportPath)
    local logPath = getFullLogPath()
    local content = safeReadFile(logPath)
    
    if content and content ~= "" then
        if safeWriteFile(exportPath, content) then
            writeLog(Logger.LEVEL.INFO, "Logger", "日志导出成功", {
                export_path = exportPath,
                original_path = logPath
            })
            return true
        end
    end
    
    writeLog(Logger.LEVEL.ERROR, "Logger", "日志导出失败", {
        export_path = exportPath,
        original_path = logPath
    })
    return false
end

-- 设置全局引用
_G.Logger = Logger

return Logger