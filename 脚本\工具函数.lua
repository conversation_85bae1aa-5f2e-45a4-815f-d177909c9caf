--[[
工具函数库
包含常用的时间处理和界面导航函数
]]--

-- 【修复】移除循环依赖，使用延迟加载
-- local 自动重启游戏 = require("自动重启游戏") -- 移除直接依赖

--[[
获取当前小时
功能：获取系统当前小时数（24小时制）
返回：0-23的整数
]]--
function 获取当前小时()
    local 时间 = os.date("*t")
    return tonumber(时间.hour)
end

--[[
获取当前分钟
功能：获取系统当前分钟数
返回：0-59的整数
]]--
function 获取当前分钟()
    local 时间 = os.date("*t")
    return tonumber(时间.min)
end

--[[
在时间段内
功能：判断当前时间是否在指定的时间段内
参数：
- 当前小时：当前的小时数
- 开始时间：时间段的开始小时
- 结束时间：时间段的结束小时
返回：true/false
]]--
function 在时间段内(当前小时, 开始时间, 结束时间)
    -- 如果开始时间小于结束时间，表示时间段在同一天内
    if 开始时间 < 结束时间 then
        return 当前小时 >= 开始时间 and 当前小时 < 结束时间
    -- 如果开始时间大于结束时间，表示时间段跨越午夜
    elseif 开始时间 > 结束时间 then
        return 当前小时 >= 开始时间 or 当前小时 < 结束时间
    -- 如果开始时间等于结束时间，表示整整24小时
    else
        return true
    end
end

--[[
尝试返回大厅
功能：通用的返回大厅功能
特点：
- 支持多种返回按钮的识别
- 有固定坐标点击作为备选方案
- 最多尝试7次
- 提供详细的显示信息输出
参数：
- log_prefix：显示信息前缀
返回：是否成功返回大厅
]]--
function 尝试返回大厅(log_prefix)
    log_prefix = log_prefix or "[返回逻辑]"
    local 返回大厅尝试次数 = 0
    local 最大返回尝试 = 7 -- 尝试次数可以根据需要调整
    
    -- 循环尝试返回大厅
    while 返回大厅尝试次数 < 最大返回尝试 do
        返回大厅尝试次数 = 返回大厅尝试次数 + 1
        
        -- 检查是否已在大厅（通过右上角图标判断）
        local idx, x, y = findPic(1192, 0, 1279, 112, "到时间_返回大厅.png", "101010", 0, 0.7)
        
        -- 成功返回到大厅
        if x > -1 and y > -1 then
            sleep(1000)
            return true -- 成功返回
        else
            local acted_this_return_loop = false
            
            -- 尝试点击左上角返回按钮（多个场景通用）
            local idx_return, x_return, y_return = findPic(0, 0, 165, 72, "领取邮件_交易行返回大厅.png|领取邮件_交易行返回大厅1.png|特勤处_返回特勤处大厅.png", "101010", 0, 0.7)
            if x_return > -1 and y_return > -1 then
                tap(x_return, y_return)
                sleep(2000)
                acted_this_return_loop = true
            end
            
            -- 如果没找到返回按钮，尝试点击固定坐标
            if not acted_this_return_loop then
                tap(91, 31) -- 通用返回按钮位置
                sleep(2000)
                acted_this_return_loop = true
            end
        end
        sleep(1000) -- 每次尝试后等待
    end
    
    显示信息(log_prefix .. " ✗ 达到最大返回大厅尝试次数，可能未成功返回。", false)
    

    显示信息("⚠️ 连续返回大厅失败，根据设置不执行重启。", false)
    
    return false -- 返回失败
end

-- 创建目录（支持多级目录）
-- 功能：创建指定路径的目录（自动创建缺失的父级目录）
-- 参数：dir_path - 要创建的目录路径（如"/data/logs"）
-- 返回：true-成功，false-失败
function makeDir(dir_path)
    local lfs = require 'lfs'
    local path = ''
    for folder in string.gmatch(dir_path, '[^/\\]+') do
        path = path .. folder .. '/'
        if lfs.attributes(path) then
            if lfs.attributes(path, 'mode') ~= 'directory' then
                return false -- 路径存在但不是目录
            end
        else
            if not lfs.mkdir(path) then
                return false -- 创建目录失败
            end
        end
    end
    return true
end

-- 【性能优化】配置管理相关函数 - 使用缓存和预计算路径
local 配置缓存 = {
    配置目录路径 = nil,

    -- 初始化配置目录（只执行一次）
    初始化配置目录 = function(self)
        if not self.配置目录路径 then
            local 存储路径 = getWorkPath()
            self.配置目录路径 = 存储路径 .. "/三角洲行动定制/"
            -- 确保目录存在
            makeDir(self.配置目录路径)
        end
        return self.配置目录路径
    end
}

-- 优化后的获取配置文件路径函数
local function 获取配置文件路径(文件名)
    local 配置目录 = 配置缓存:初始化配置目录()
    return 配置目录 .. 文件名
end

-- 检查文件是否存在
local function 文件是否存在(路径)
    local file = io.open(路径, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- 【注意】卡密读取函数已移至网络验证模块统一管理，此处不再重复定义
-- 如需使用卡密功能，请调用网络验证模块中的 保存卡密() 和 读取卡密() 函数

-- 【性能优化】保存三角洲UI配置
function 保存三角洲配置(配置数据)
    local 文件路径 = 获取配置文件路径("triangle_settings.json")

    local file = io.open(文件路径, "w")
    if file then
        local json_str = jsonLib.encode(配置数据)
        file:write(json_str)
        file:close()

        -- 简化验证：只检查文件是否存在
        return 文件是否存在(文件路径)
    end
    return false
end

-- 【性能优化】读取三角洲UI配置 - 减少调试输出，优化错误处理
function 读取三角洲配置()
    local 文件路径 = 获取配置文件路径("triangle_settings.json")

    if not 文件是否存在(文件路径) then
        return 获取默认配置()
    end

    local file = io.open(文件路径, "r")
    if file then
        local content = file:read("*all")
        file:close()

        if content and content ~= "" then
            local ok, 配置 = pcall(jsonLib.decode, content)
            if ok and type(配置) == "table" then
                return 配置
            end
        end
    end

    -- 任何错误情况都返回默认配置
    return 获取默认配置()
end

-- 【性能优化】获取默认配置 - 移除调试输出
function 获取默认配置()
    return {
        ["多选框_选择扫货功能"] = false,
        ["多选框_出售子弹"] = false,
        ["子弹类型"] = 0,  -- 0代表9*19，1代表5.7*28
        ["多选框_滚仓扫货"] = false,
        ["扫货_自动挂机"] = false,
        ["特勤处_制作装备"] = false,
        ["输入框_扫货输入价格"] = "",
        ["输入框_抢购次数"] = "",
        ["输入框_出售价格"] = "",
        ["滚仓_子弹数量"] = "",
        ["滚仓_子弹价格"] = "",
        ["出售_开始时间"] = "13",
        ["出售_结束时间"] = "19"
    }
end

-- 【性能优化】保存用户配置 - 减少调试输出
function 保存用户配置(配置数据)
    local file = io.open(获取配置文件路径("user_settings.json"), "w")
    if file then
        local json_str = jsonLib.encode(配置数据)
        file:write(json_str)
        file:close()
        return true
    end
    return false
end

-- 读取用户配置
function 读取用户配置()
    local file = io.open(获取配置文件路径("user_settings.json"), "r")
    if file then
        local content = file:read("*all")
        file:close()
        if content and content ~= "" then
            local ok, 配置 = pcall(jsonLib.decode, content)
            if ok then
                print("成功读取用户配置")
                return 配置
            end
        end
    end
    print("读取用户配置失败，使用默认配置")
    return {
        自动抢购 = true,
        自动出售 = true,
        自动重启 = true,
        显示提示 = true,
        特勤处制造 = false
    }
end


-- 导出所有函数
return {
    获取当前小时 = 获取当前小时,
    获取当前分钟 = 获取当前分钟,
    在时间段内 = 在时间段内,
    尝试返回大厅 = 尝试返回大厅
}