-- OCR功能模块
-- 使用懒人精灵内置OCR功能，符合API文档规范

-- 【修复】使用懒人精灵内置的OCR功能，替代第三方OCR库
-- 根据懒人API文档，使用内置的神经网络OCR功能

-- 初始化OCR模型（使用懒人内置的飞桨OCR模型）
local ocr_initialized = false

local function init_ocr()
    if not ocr_initialized then
        -- 【符合文档】使用PaddleOcr.loadModel加载默认模型
        local success = PaddleOcr.loadModel(true) -- true表示使用onnx模型（精度最高）
        if success then
            print("✅ OCR模块初始化成功：使用懒人内置飞桨OCR模型")
            ocr_initialized = true
        else
            print("❌ OCR模块初始化失败")
            ocr_initialized = false
        end
    end
    return ocr_initialized
end

-- OCR识别函数（用于识别屏幕指定区域的数字）
-- 参数：x1，y1，x2，y2（识别区域坐标）、zi（预留参数，当前未使用）
-- 返回：识别到的数字字符串
local function ocr_start(x1, y1, x2, y2, zi)
    -- 确保OCR已初始化
    if not init_ocr() then
        return ""
    end

    -- 【符合文档】使用PaddleOcr.detect进行OCR识别
    local result_json = PaddleOcr.detect(x1, y1, x2, y2)

    if result_json and result_json ~= "" then
        -- 解析JSON结果，提取数字
        local success, result_table = pcall(function()
            return require("json").decode(result_json)
        end)

        if success and result_table and type(result_table) == "table" then
            local numbers = ""
            for _, item in ipairs(result_table) do
                if item.text then
                    -- 提取文本中的数字
                    local extracted_numbers = string.gsub(item.text, "[^0-9]", "")
                    numbers = numbers .. extracted_numbers
                end
            end
            return numbers
        else
            -- 如果JSON解析失败，尝试直接从字符串中提取数字
            local numbers = string.gsub(result_json, "[^0-9]", "")
            return numbers
        end
    end

    return "" -- 识别失败返回空字符串
end

local function ocr_时间(x1, y1, x2, y2, zi)
    -- 确保OCR已初始化
    if not init_ocr() then
        return ""
    end

    -- 【符合文档】使用PaddleOcr.detect进行OCR识别
    local result_json = PaddleOcr.detect(x1, y1, x2, y2)

    if result_json and result_json ~= "" then
        -- 解析JSON结果，提取时间格式（数字和冒号）
        local success, result_table = pcall(function()
            return require("json").decode(result_json)
        end)

        if success and result_table and type(result_table) == "table" then
            local time_text = ""
            for _, item in ipairs(result_table) do
                if item.text then
                    -- 提取文本中的数字和冒号
                    local extracted_time = string.gsub(item.text, "[^0-9:]", "")
                    time_text = time_text .. extracted_time
                end
            end
            return time_text
        else
            -- 如果JSON解析失败，尝试直接从字符串中提取时间格式
            local time_text = string.gsub(result_json, "[^0-9:]", "")
            return time_text
        end
    end

    return "" -- 识别失败返回空字符串
end

-- 返回一个包含OCR函数的表
return {
    ocr_start = ocr_start,
    ocr_时间 = ocr_时间,
    init_ocr = init_ocr -- 暴露初始化函数供外部调用
}